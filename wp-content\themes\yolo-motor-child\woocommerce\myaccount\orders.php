<?php

/**
 * Orders
 *
 * Shows orders on the account page.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/orders.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.2.0
 */

defined('ABSPATH') || exit;
// $has_orders = false;
// function custom_show_account_orders() {
//     // Check if user is logged in
//     if ( is_user_logged_in() ) {
//         $has_orders = wc_get_customer_order_count( get_current_user_id() ) > 0;
//         // Trigger the WooCommerce action to load the orders template
//         do_action( 'woocommerce_before_account_orders', $has_orders );
//     }
// }

// // Hook this function to any WooCommerce-compatible location, like the My Account page
// add_action( 'woocommerce_account_dashboard', 'custom_show_account_orders' );
$my_orders_columns = apply_filters(
	'woocommerce_my_account_my_orders_columns',
	array(
		'order-number'  => esc_html__('Order', 'woocommerce'),
		'order-date'    => esc_html__('Date', 'woocommerce'),
		'order-status'  => esc_html__('Status', 'woocommerce'),
		'order-total'   => esc_html__('Total', 'woocommerce'),
		'order-actions' => '&nbsp;',
	)
);

$b2b_admin_id = get_main_b2b_admin_id(get_current_user_id());
$sub_user_ids = get_users(array(
    'meta_key' => '_parent_admin_id',
    'meta_value' => $b2b_admin_id,
    'fields' => 'ID',
));
$sub_user_ids[] = $b2b_admin_id;
// var_dump($sub_user_ids);
// exit;

$customer_orders = get_posts(
	apply_filters(
		'woocommerce_my_account_my_orders_query',
		array(
			'meta_key'    => '_customer_user',
			'meta_query'  => array(
                array(
                    'key'     => '_customer_user',
                    'value'   => $sub_user_ids, // Pass the array of user IDs here
                    'compare' => 'IN',          // Use IN to match any of the user IDs
                )
			),
			'post_type'   => wc_get_order_types('view-orders'),
			'post_status' => array_keys(wc_get_order_statuses()),
		)
	)
);

?>
<div class="table_responsive">
    <table class="woocommerce-orders-table woocommerce-MyAccount-orders shop_table my_account_orders account-orders-table">
        <thead>
            <tr>
                <?php foreach (wc_get_account_orders_columns() as $column_id => $column_name) : ?>
                    <th scope="col" class="woocommerce-orders-table__header woocommerce-orders-table__header-<?php echo esc_attr($column_id); ?>"><span class="nobr"><?php echo esc_html($column_name); ?></span></th>
                <?php endforeach; ?>
            </tr>
        </thead>

        <tbody>
            <?php
            foreach ($customer_orders as $customer_order) :
                $order      = wc_get_order($customer_order); // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
                $item_count = $order->get_item_count();
                $purchase_order_number = get_post_meta($customer_order->ID, '_purchase_order_number', true);
            ?>
                <tr class="woocommerce-orders-table__row woocommerce-orders-table__row--status-<?php echo esc_attr( $order->get_status() ); ?> order">
                        <?php foreach ( wc_get_account_orders_columns() as $column_id => $column_name ) :
                            $is_order_number = 'order-number' === $column_id;
                        ?>
                            <?php if ( $is_order_number ) : ?>
                                <th class="woocommerce-orders-table__cell woocommerce-orders-table__cell-<?php echo esc_attr( $column_id ); ?>" data-title="<?php echo esc_attr( $column_name ); ?>" scope="row">
                            <?php else : ?>
                                <td class="woocommerce-orders-table__cell woocommerce-orders-table__cell-<?php echo esc_attr( $column_id ); ?>" data-title="<?php echo esc_attr( $column_name ); ?>">
                            <?php endif; ?>

                                <?php if ( has_action( 'woocommerce_my_account_my_orders_column_' . $column_id ) ) : ?>
                                    <?php do_action( 'woocommerce_my_account_my_orders_column_' . $column_id, $order ); ?>

                                <?php elseif ( $is_order_number ) : ?>
                                    <?php /* translators: %s: the order number, usually accompanied by a leading # */ ?>
                                    <a class="link link-orange orange" href="<?php echo esc_url( $order->get_view_order_url() ); ?>" aria-label="<?php echo esc_attr( sprintf( __( 'View order number %s', 'woocommerce' ), $order->get_order_number() ) ); ?>">
                                        <?php 
                                        // echo esc_html( _x( '#', 'hash before order number', 'woocommerce' ) . $purchase_order_number ); 
                                        echo esc_html($purchase_order_number ); 
                                        ?>
                                    </a>

                                <?php elseif ( 'order-date' === $column_id ) : ?>
                                    <time datetime="<?php echo esc_attr( $order->get_date_created()->date( 'c' ) ); ?>"><?php echo esc_html( wc_format_datetime( $order->get_date_created() ) ); ?></time>

                                <?php elseif ( 'order-status' === $column_id ) : ?>
                                    <?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>

                                <?php elseif ( 'order-total' === $column_id ) : ?>
                                    <?php
                                    /* translators: 1: formatted order total 2: total order items */
                                    echo wp_kses_post( sprintf( _n( '%1$s for %2$s item', '%1$s for %2$s items', $item_count, 'woocommerce' ), $order->get_formatted_order_total(), $item_count ) );
                                    ?>

                                <?php elseif ( 'order-actions' === $column_id ) : ?>
                                    <?php
                                    $actions = wc_get_account_orders_actions( $order );

                                    if ( ! empty( $actions ) ) {
                                        foreach ( $actions as $key => $action ) { // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
                                            /* translators: %s: order number */
                                            echo '<a href="' . esc_url( $action['url'] ) . '" class="link link-orange orange text-underline" aria-label="' . esc_attr( sprintf( __( 'View order number %s', 'woocommerce' ), $order->get_order_number() ) ) . '">' . esc_html( $action['name'] ) . '</a>';
                                        }
                                    }
                                    ?>
                                <?php endif; ?>

                            <?php if ( $is_order_number ) : ?>
                                </th>
                            <?php else : ?>
                                </td>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
</div>

<?php
// add_filter( 'woocommerce_can_user_view_order', 'allow_subuser_to_view_admin_order', 999, 2 );

// function allow_subuser_to_view_admin_order( $can_view, $order_id ) {

//     var_dump($order_id);
//     exit;
//     $current_user_id = get_current_user_id();
//     $order = wc_get_order( $order_id );

//     if ( ! $order ) {
//         return $can_view; // If order doesn't exist, return the default access
//     }
//     $main_admin_id = get_main_b2b_admin_id( $current_user_id );
//     if ( $order->get_user_id() === $current_user_id || $order->get_user_id() === $main_admin_id ) {
//         return true;
//     }
//     return $can_view;
// }

// add_filter('woocommerce_is_order_owner', 'allow_subuser_to_view_admin_order', 10, 2);

// function allow_subuser_to_view_admin_order($is_order_owner, $order) {
//     $current_user_id = get_current_user_id();
//     $order_user_id = $order->get_user_id();

//     // Check if current user is a sub-user with a main admin
//     $main_admin_id = get_main_b2b_admin_id($current_user_id); // This should return the admin's ID for sub-users or false for admins

//     // Allow sub-users to view their admin's orders
//     if ($main_admin_id && $main_admin_id === $order_user_id) {
//         return true;
//     }

//     // Default behavior for other users
//     return $is_order_owner;
// }
