<?php
/**
 * Product quantity inputs
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/global/quantity-input.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you (the theme developer).
 * will need to copy the new files to your theme to maintain compatibility. We try to do this.
 * as little as possible, but it does happen. When this occurs the version of the template file will.
 * be bumped and the readme will list any important changes.
 *
 * @see 	    http://docs.woothemes.com/document/template-structure/
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     7.8.0
 */

if (!defined('ABSPATH')) {
	exit; // Exit if accessed directly
}
if ( $max_value && $min_value === $max_value ) {
	?>
	<div class="quantity hidden">
		<input type="hidden" id="<?php echo esc_attr( $input_id ); ?>" class="qty" name="<?php echo esc_attr( $input_name ); ?>" value="<?php echo esc_attr( $min_value ); ?>" />
	</div>
	<?php
} else {
	?>
	<div class="quantity">
		<div class="quantity-inner">
			<input type="number"
				   id="<?php echo esc_attr( $input_id ); ?>"
				   class="<?php echo esc_attr( join( ' ', (array) $classes ) ); ?>"
				   step="<?php echo esc_attr($step); ?>"
			       min="<?php echo esc_attr( $min_value ); ?>" 
			       max="<?php echo esc_attr( 0 < $max_value ? $max_value : '' ); ?>"
			       name="<?php echo esc_attr($input_name); ?>"
			       value="<?php echo esc_attr($input_value); ?>"
			       title="<?php echo esc_attr_x('Qty', 'Product quantity input tooltip', 'yolo-motor') ?>" 
			       size="4"
			       inputmode="<?php echo esc_attr( $inputmode ); ?>"
			       />
		</div>
	</div>
<?php }?>
