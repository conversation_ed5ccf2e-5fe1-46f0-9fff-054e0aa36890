<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * @created    25/12/2015
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

	$prefix = 'yolo_';

	$categories       = get_categories(array( 'taxonomy' => 'product_cat' ));
	$category_content = yolo_categories_binder($categories, '0');
	wp_enqueue_script('jquery-scrollbar');
	wp_enqueue_style('jquery-scrollbar');
?>
<div class="search-with-category header-customize-item" data-hint-message="<?php esc_attr_e( 'Enter keyword to search', 'yolo-motor' ) ?>">
	<div class="search-with-category-inner search-box">
		<div class="form-search-left">
			<span data-id="-1"><?php esc_html_e( 'Categories', 'yolo-motor' ); ?></span>
			<?php if (!empty($category_content)) : ?>
				<?php echo wp_kses_post($category_content) ?>
			<?php endif; ?>
		</div>
		<div class="form-search-right">
			<input type="text" name="s"/>
			<button type="button"><i class="wicon fa fa-search"></i></button>
		</div>
	</div>
</div>