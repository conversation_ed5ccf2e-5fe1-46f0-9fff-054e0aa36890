<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/
$prefix = 'yolo_';

$header_class = array('yolo-main-header', 'header-sidebar');
$page_menu = get_post_meta(get_the_ID(), $prefix . 'page_menu',true);
?>
<header id="yolo-header" class="<?php echo join(' ', $header_class) ?>">
	<div class="vertical-header-wrapper">
		<!-- Top custom navigation (left) -->
		<div class="ft">
			<?php yolo_get_template('header/header-customize-left' ); ?>
		</div>
		<div class="header-top">
			<?php yolo_get_template('header/header-logo' ); ?>
		</div>
		<!-- Bottom custom navigation (right) -->
		<div class="fb">
			<?php yolo_get_template('header/header-customize-right' ); ?>
		</div>
		<div class="header-bottom">
			<?php if (has_nav_menu('primary')) : ?>
				<div id="primary-menu" class="menu-wrapper">
					<?php
					wp_nav_menu( array(
						'theme_location'  => 'primary',
						'menu'            => $page_menu,
						'container'       => '',
						'menu_class'      => 'yolo-main-menu nav-collapse navbar-nav vertical-megamenu',
						'menu_id'         => 'main-menu',
						'fallback_cb'     => 'please_set_menu',
						'walker'          => new Yolo_MegaMenu_Walker(),
					) );
					
					?>
				</div>
			<?php endif; ?>
			<?php yolo_get_template('header/header-customize-nav' ); ?>
		</div>
	</div>
</header>