import { registerBlockVariation } from '@wordpress/blocks';
import { __ } from '@wordpress/i18n';

wp.hooks.addFilter(
    'woocommerce_blocks_cart_item',
    'custom/cart-item-layout',
    (OriginalCartItem) => {
        return (props) => {
            const { product, cartItem } = props;

            return (
                <tr className="custom-cart-item">
                    <td className="custom-cart-item__details">
                        <div className="custom-cart-item__image">{product.images[0]}</div>
                        <div className="custom-cart-item__info">
                            <h4>{product.name}</h4>
                            <p>{product.description}</p>
                            <span>{product.prices}</span>
                        </div>
                    </td>
                    <td className="custom-cart-item__quantity">
                        {cartItem.quantity}
                    </td>
                    <td className="custom-cart-item__subtotal">
                        {cartItem.subtotal}
                    </td>
                    <td className="custom-cart-item__remove">
                        <button
                            onClick={() => props.onRemove(cartItem.key)}
                            aria-label={__('Remove', 'woocommerce')}
                        >
                            ×
                        </button>
                    </td>
                </tr>
            );
        };
    }
);
