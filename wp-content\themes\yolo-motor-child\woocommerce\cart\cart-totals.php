<?php
/**
 * Cart totals
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/cart/cart-totals.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 2.3.6
 */

defined( 'ABSPATH' ) || exit;

?>
<div class="cart_totals <?php echo ( WC()->customer->has_calculated_shipping() ) ? 'calculated_shipping' : ''; ?>">

	<?php do_action( 'woocommerce_before_cart_totals' ); ?>

	<h2><?php esc_html_e( 'Cart totals', 'woocommerce' ); ?></h2>

	<table cellspacing="0" class="shop_table shop_table_responsive">

		<tr class="cart-subtotal2">
			<th><?php esc_html_e( 'Item Subtotal ', 'woocommerce' ); ?></th>
            <th>&nbsp;</th>
			<td data-title="<?php esc_attr_e( 'Items Subtotal', 'woocommerce' ); ?>"><?php wc_cart_totals_subtotal_html(); ?></td>
		</tr>

		<tr class="cart-subtotal2">
			<th><?php esc_html_e( 'Delivery', 'woocommerce' ); ?></th>
            <th>&nbsp;</th>
			<td style="text-align: right"><?php esc_html_e( 'Later', 'woocommerce' ); ?></td>
		</tr>

		<tr class="cart-subtotal2 sap-tax-row">
			<th><?php esc_html_e( 'Tax Total', 'woocommerce' ); ?></th>
            <th>&nbsp;</th>
			<td style="text-align: right;font-weight: 500;">
				<?php
				// Calculate SAP tax total for logged-in users
				if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
					$sap_tax_total = 0;
					$user_id = get_current_user_id();
					$has_tax_data = false;

					// Calculate tax total based on SAP pricing for each cart item
					foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
						$product = $cart_item['data'];
						if ( ! $product || ! ($product instanceof WC_Product) ) {
							continue;
						}

						$quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

						// Get SAP pricing for this product with actual cart quantity
						$pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, $quantity );

						if ( is_array( $pricing ) && isset( $pricing['sales_tax'] ) ) {
							// Convert SAP tax response (divide by 100) and add to total
							$tax_value_decimal = (float)$pricing['sales_tax'] / 100;
							$sap_tax_total += $tax_value_decimal;
							$has_tax_data = true;
						}
					}

					if ( $has_tax_data ) {
						// Format the SAP tax total with proper currency
						$company_code = get_user_meta($user_id, '_companycode', true);
						if ($company_code === '3090') {
							// US customers - show in USD
							echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">$</span>' . number_format($sap_tax_total, 2, '.', '') . '</bdi></span>';
						} else {
							// Non-US customers - show in EUR
							echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">€</span>' . number_format($sap_tax_total, 2, '.', '') . '</bdi></span>';
						}
					} else {
						// No tax data available
						esc_html_e( 'Calculated at checkout', 'woocommerce' );
					}
				} else {
					// Fallback for non-logged-in users
					esc_html_e( 'Calculated at checkout', 'woocommerce' );
				}
				?>
			</td>
		</tr>

		<?php foreach ( WC()->cart->get_coupons() as $code => $coupon ) : ?>
			<tr class="cart-discount coupon-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
				<th><?php wc_cart_totals_coupon_label( $coupon ); ?></th>
                <th>&nbsp;</th>
				<td data-title="<?php echo esc_attr( wc_cart_totals_coupon_label( $coupon, false ) ); ?>"><?php wc_cart_totals_coupon_html( $coupon ); ?></td>
			</tr>
		<?php endforeach; ?>

		<?php if ( WC()->cart->needs_shipping() && WC()->cart->show_shipping() ) : ?>

			<?php // do_action( 'woocommerce_cart_totals_before_shipping' ); ?>

			<?php // wc_cart_totals_shipping_html(); ?>

			<?php // do_action( 'woocommerce_cart_totals_after_shipping' ); ?>

		<?php elseif ( WC()->cart->needs_shipping() && 'yes' === get_option( 'woocommerce_enable_shipping_calc' ) ) : ?>

			<!-- <tr class="shipping">
				<th><?php // esc_html_e( 'Shipping', 'woocommerce' ); ?></th>
				<td data-title="<?php // esc_attr_e( 'Shipping', 'woocommerce' ); ?>"><?php // woocommerce_shipping_calculator(); ?></td>
			</tr> -->

		<?php endif; ?>

		<?php foreach ( WC()->cart->get_fees() as $fee ) : ?>
			<tr class="fee">
				<th><?php echo esc_html( $fee->name ); ?></th>
				<td data-title="<?php echo esc_attr( $fee->name ); ?>"><?php wc_cart_totals_fee_html( $fee ); ?></td>
			</tr>
		<?php endforeach; ?>

		<?php
		if ( wc_tax_enabled() && ! WC()->cart->display_prices_including_tax() ) {
			$taxable_address = WC()->customer->get_taxable_address();
			$estimated_text  = '';

			if ( WC()->customer->is_customer_outside_base() && ! WC()->customer->has_calculated_shipping() ) {
				/* translators: %s location. */
				$estimated_text = sprintf( ' <small>' . esc_html__( '(estimated for %s)', 'woocommerce' ) . '</small>', WC()->countries->estimated_for_prefix( $taxable_address[0] ) . WC()->countries->countries[ $taxable_address[0] ] );
			}

			if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) {
				foreach ( WC()->cart->get_tax_totals() as $code => $tax ) { // phpcs:ignore WordPress.WP.GlobalVariablesOverride.Prohibited
					?>
					<tr class="tax-rate tax-rate-<?php echo esc_attr( sanitize_title( $code ) ); ?>">
						<th><?php echo esc_html( $tax->label ) . $estimated_text; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?></th>
						<td data-title="<?php echo esc_attr( $tax->label ); ?>"><?php echo wp_kses_post( $tax->formatted_amount ); ?></td>
					</tr>
					<?php
				}
			} else {
				?>
				<tr class="tax-total">
					<th><?php echo esc_html( WC()->countries->tax_or_vat() ) . $estimated_text; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped ?></th>
                    <th>&nbsp;</th>
					<td data-title="<?php echo esc_attr( WC()->countries->tax_or_vat() ); ?>"><?php wc_cart_totals_taxes_total_html(); ?></td>
				</tr>
				<?php
			}
		}
		?>

		<?php do_action( 'woocommerce_cart_totals_before_order_total' ); ?>

		<tr class="order-total">
			<th><?php esc_html_e( 'Estimated Total', 'woocommerce' ); ?></th>
            <th>&nbsp;</th>
			<td data-title="<?php esc_attr_e( 'Total', 'woocommerce' ); ?>">
				<?php
				// Calculate SAP total for logged-in users
				if ( is_user_logged_in() && function_exists('hytec_sap_get_or_fetch_pricing_with_quantity') ) {
					$sap_subtotal = 0;
					$sap_tax_total = 0;
					$user_id = get_current_user_id();
					$debug_items = array();

					// Calculate subtotal and tax based on SAP pricing for each cart item
					foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
						$product = $cart_item['data'];
						if ( ! $product || ! ($product instanceof WC_Product) ) {
							continue;
						}

						$quantity = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

						// Get SAP pricing for this product with actual cart quantity
						$pricing = hytec_sap_get_or_fetch_pricing_with_quantity( $product, $user_id, $quantity );

						if ( is_array( $pricing ) && isset( $pricing['net_value'] ) && $pricing['net_value'] !== '' ) {
							// Convert SAP response (divide by 100) and add to subtotal
							$net_value_decimal = (float)$pricing['net_value'] / 100;
							$sap_subtotal += $net_value_decimal;
							$debug_items[] = $product->get_sku() . ': €' . number_format($net_value_decimal, 2);

							// Add tax if available
							if ( isset( $pricing['sales_tax'] ) && $pricing['sales_tax'] !== '' ) {
								$tax_value_decimal = (float)$pricing['sales_tax'] / 100;
								$sap_tax_total += $tax_value_decimal;
							}
						} else {
							// Fallback to WooCommerce price if SAP not available
							$fallback_price = $product->get_price() * $quantity;
							$sap_subtotal += $fallback_price;
							$debug_items[] = $product->get_sku() . ': €' . number_format($fallback_price, 2) . ' (fallback)';
						}
					}

					// Calculate total (subtotal + tax)
					$sap_total = $sap_subtotal + $sap_tax_total;

					// Format the SAP total with proper currency
					$company_code = get_user_meta($user_id, '_companycode', true);
					if ($company_code === '3090') {
						// US customers - show in USD
						echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">$</span>' . number_format($sap_total, 2, '.', '') . '</bdi></span>';
					} else {
						// Non-US customers - show in EUR
						echo '<span class="woocommerce-Price-amount amount"><bdi><span class="woocommerce-Price-currencySymbol">€</span>' . number_format($sap_total, 2, '.', '') . '</bdi></span>';
					}


				} else {
					// Fallback for non-logged-in users
					wc_cart_totals_order_total_html();
				}
				?>
			</td>
		</tr>

		<?php do_action( 'woocommerce_cart_totals_after_order_total' ); ?>

	</table>

    <div class="woocommerce-privacy-policy-text" style="padding: 0;transform: none;position: relative;z-index: 111111111111;font-size: 13px;">Applicable shipping with be calculated with your company rep when your order is received on our system.</div>

	<div class="wc-proceed-to-checkout">
		<?php do_action( 'woocommerce_proceed_to_checkout' ); ?>
	</div>

	<?php do_action( 'woocommerce_after_cart_totals' ); ?>

</div>
