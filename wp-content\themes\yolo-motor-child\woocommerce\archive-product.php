<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive.
 *
 * Override this template by copying it to yourtheme/woocommerce/archive-product.php
 *
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     8.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $yolo_woocommerce_loop,$yolo_motor_options;

/* Get style for shop page*/
$archive_product_style = isset($yolo_motor_options['archive_product_style']) ? $yolo_motor_options['archive_product_style'] : 'style_2';
$archive_product_display = isset($yolo_motor_options['archive_product_display']) ? $yolo_motor_options['archive_product_display'] :'fitRows';
$layout_style = isset($yolo_motor_options['archive_product_layout']) ? $yolo_motor_options['archive_product_layout'] :'container';
$sidebar = $yolo_motor_options['archive_product_sidebar'];
$sidebar_width = $yolo_motor_options['archive_product_sidebar_width'];
$left_sidebar   = $yolo_motor_options['archive_product_left_sidebar'];
$right_sidebar  = $yolo_motor_options['archive_product_right_sidebar'];
$archive_display_columns = isset($yolo_motor_options['product_display_columns']) ? $yolo_motor_options['product_display_columns'] : '4';
$yolo_woocommerce_loop['columns'] = $archive_display_columns;
$product_rating = $yolo_motor_options['product_show_rating'];
$yolo_woocommerce_loop['rating'] = $product_rating;

$sidebar_col = 'col-md-3';
if ($sidebar_width == 'large') {
    $sidebar_col = 'col-md-4';
}
if ( $archive_product_style == 'style_1' ) {
    $sidebar_col = 'col-md-12';
}

$content_col_number = 12;

if ( $archive_product_style == 'style_2' ) {
    if (is_active_sidebar( $left_sidebar ) && (($sidebar == 'both') || ($sidebar == 'left'))) {
        if ($sidebar_width == 'large') {
            $content_col_number -= 4;
        }
        else {
            $content_col_number -= 3;
        }
    }
    if (is_active_sidebar( $right_sidebar ) && (($sidebar == 'both') || ($sidebar == 'right'))) {
        if ($sidebar_width == 'large') {
            $content_col_number -= 4;
        }
        else {
            $content_col_number -= 3;
        }
    }
}

$content_col = 'col-md-' . $content_col_number;
if (($content_col_number == 12) && ($layout_style == 'full')) {
    $content_col = '';
}

$archive_class = array('archive-product-wrap','clearfix');
$archive_class[] = 'layout-' . $layout_style;

$product_filter_class          = array();
$product_show_result_count     = isset($yolo_motor_options['product_show_result_count'])? $yolo_motor_options['product_show_result_count']: true ;
$product_show_catalog_ordering = isset($yolo_motor_options['product_show_catalog_ordering'])? $yolo_motor_options['product_show_catalog_ordering']: true;
if (($product_show_result_count == false) && ($product_show_catalog_ordering == false) ) {
    $product_filter_class[] = 'catalog-filter-invisible';
} else {
    if ($product_show_result_count == false) {
        $product_filter_class[] = 'result-count-invisible';
    }
    if ($product_show_catalog_ordering == false) {
        $product_filter_class[] = 'catalog-ordering-invisible';
    }
}
$show_categories = isset($yolo_motor_options['show_categories']) ? $yolo_motor_options['show_categories'] : true;
$show_search = isset($yolo_motor_options['show_search']) ? $yolo_motor_options['show_search'] : true;
$show_filters = isset($yolo_motor_options['show_filters']) ? $yolo_motor_options['show_filters'] :true;
if ( isset($_REQUEST['shop_load']) && $_REQUEST['shop_load'] == 'full' ){
    get_template_part( 'woocommerce/content-product-ajax' );
} else {

get_header( 'shop' ); ?>
    <script type="text/javascript">
        var yolo_ajax_filter    = '<?php echo $yolo_motor_options['yolo_ajax_filter'] ? $yolo_motor_options['yolo_ajax_filter'] : '';?>';
        /* When show all products*/
        var yolo_all_products   = '<?php echo esc_html__("All products loaded", "yolo-motor");?>';
        var yolo_style          = '<?php echo $yolo_motor_options["archive_product_style"];?>';
    </script>
<?php
    wp_enqueue_script('isotope');
    wp_enqueue_script('yolo-shop-filters');
/**
 * @hooked - yolo_archive_product_heading - 5
 **/
do_action('yolo_before_archive_product');
?>
<main role="main" class="site-content-archive-product" data-product-style = "<?php echo esc_attr($archive_product_display);?>">
    <?php
    /**
     * @hooked - yolo_shop_page_content - 5
     **/
    do_action('yolo_before_archive_product_listing');
    ?>

    <?php if ($layout_style != 'full') : ?>
        <div class="<?php echo esc_attr($layout_style) ?> clearfix">
    <?php endif;?>

            <?php if (($content_col_number != 12) || ($layout_style != 'full')): ?>
                <div class="row clearfix">
            <?php endif;?>
            <div class="<?php echo esc_attr($content_col) ?> col-xs-12">
                <div class="<?php echo join(' ',$archive_class); ?>">
                    <?php if ( $archive_product_style == 'style_1' ):?>
                            <div class="filter-row">
                                <div class="flex aic jcsb w100">
                                    <div class="flex aic w100 flex-mob-column">
                                        <input type="text" id="custom-search" placeholder="Search products">
                                        <div class="mx-15 midGray right-border h100 desktop" style="height: 40px;"></div>
                                        <div class="flex aic flex-mob-column ail-mob w100-mob mt-mob-2">
                                            <h4 class="f-16 mr-2 mb-mob-1">Product Status</h4>
                                            <div class="flex aic">
                                                <span class="flex aic custom-check mr-2">
                                                    <input type="radio" id="in-stock" name="stock" value="in_stock">
                                                    <label for="in-stock" class="ml-1 text-uppercase">In Stock Only</label>
                                                </span>
                                                <span class="flex aic custom-check">
                                                    <input type="radio" id="all-stock" name="stock" value="all" checked="">
                                                    <label for="all-stock" class="ml-1 text-uppercase">All</label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <a href="/shop/" title="" class="midGray f-12 no-wrap clear_search_results" style="white-space: nowrap;"><span class="orange">X</span> Clear Results</a>
                                </div>
                            </div>

                            <!-- Row 2: Custom Fields Filter Dropdowns -->
                            <div class="filter-row filter-selects">
                                <div class="filter-item filter-brand">
                                    <div class="mb-05 f-14 bold">Brand</div>
                                    <select class="filter_meta_options" id="brand" multiple></select>
                                </div>
                                <div class="filter-item filter-product_line">
                                    <div class="mb-05 f-14 bold">Product Line</div>
                                    <select class="filter_meta_options" id="product_line" multiple></select>
                                </div>
                                <div class="filter-item filter-product_family">
                                    <div class="mb-05 f-14 bold">Product Family</div>                                            
                                    <select class="filter_meta_options" id="product_family" multiple></select>
                                </div>
                                <!--<div class="filter-item filter-product_series">
                                    <div class="mb-05 f-14 bold">Product Series</div>
                                    <select class="filter_meta_options" id="product_series" multiple></select>
                                </div>-->
                                <div class="filter-item filter-model_size">
                                    <div class="mb-05 f-14 bold">Model Size</div>
                                    <select class="filter_meta_options" id="model_size" multiple></select>
                                </div>
                                <!-- <div class="filter-item filter-usd_price_code">
                                    <div class="mb-05 f-14 bold">Price Code</div>
                                    <select class="filter_meta_options" id="usd_price_code" multiple></select>
                                </div> -->
                                <div class="filter-item filter-ce_approved">
                                    <div class="mb-05 -14 bold">CE Approved</div>
                                    <select class="filter_meta_options" id="ce_approved" multiple></select>
                                </div>
                            </div>

                            <!-- Row 3: Stock Checkbox and Sort Dropdown -->
                            <div class="filter-row1 mb-2">
                                <button id="apply-filters" class="w100-mob">Apply All</button>
                                <hr>
                                <!--<div class="desktop">
                                    <div class="flex aic">
                                        <h4 class="f-14">Sort By</h4>
                                        <select id="sort-by" class="mx-15">
                                            <option value="default">Default Sorting</option>
                                            <option value="popularity">Sort by Popularity</option>
                                            <option value="rating">Sort by Average Rating</option>
                                            <option value="newness">Sort by Newness</option>
                                            <option value="price_low_to_high">Sort by Price: Low to High</option>
                                            <option value="price_high_to_low">Sort by Price: High to Low</option>
                                        </select>
                                        <span class="f-14 bold h4" id="pagination-info"></span>
                                    </div>
                                </div>-->
                            </div>
                        </div>

                        <div class="clearfix"></div>
                        <?php wc_get_template_part( 'content', 'product_reset' );?>
                    <?php endif;?>
                    <?php if ( have_posts() ) :
                        /**
                         * woocommerce_before_shop_loop hook
                         *
                         * @hooked woocommerce_result_count - 20
                         * @hooked woocommerce_catalog_ordering - 30
                         */
                        // do_action( 'woocommerce_before_shop_loop' );


                        /**
                         * Hook: woocommerce_shop_loop.
                         *
                         * @hooked WC_Structured_Data::generate_product_data() - 10
                         */
                        // do_action( 'woocommerce_shop_loop' );

                        // wc_get_template_part( 'content', 'product' );
                        ?>
                            <div id="products-table-wrap">
                                <div class="table_responsive">
                                    <table id="products-table" class="display">
                                        <thead>
                                            <tr>
                                                <th style="max-width: 50px" width="50px">Add To Cart</th> 
                                                <th>Materials #</th>
                                                <th>
                                                    <span class="tooltip-header">Quantity Available</span>
                                                    <!--<div class="tooltip-content">The stock status and lead times displayed below are for order estimation purposes only and may not represent exact inventory levels. If lead times or stock availability are crucial, please reach out to our customer service team for the most accurate information. Thank you!</div>-->
                                                </th>
                                                <th>
                                                    <span class="tooltip-header">List Price <span class="i-icon">i</span></span>
                                                    <div class="tooltip-content">Please note that the prices displayed are list prices and do not reflect any volume discounts or special pricing adjustments that may apply.</div>
                                                </th>
                                                <th>
                                                    <span class="tooltip-header">Net Price <span class="i-icon">i</span></span>
                                                    <div class="tooltip-content">SAP net pricing based on your customer agreement. This price may differ from the list price.</div>
                                                </th>
                                                <th>Brand</th>
                                                <th>Product Line</th>
                                                <th>Product Family</th>
                                                <th>Model/Size</th>
                                                <th>
                                                    <span class="tooltip-header">CE <span class="i-icon">i</span></span>
                                                    <div class="tooltip-content">This statement serves to clarify that items marked as CE Approved have met the necessary compliance standards. However, items not marked as CE Approved may either not require such approval or have not been verified by engineering to meet CE requirements.</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        <?php
                        
                        /**
                         * woocommerce_after_shop_loop hook
                         *
                         * @hooked woocommerce_pagination - 10
                         */
                        // do_action( 'woocommerce_after_shop_loop' );
                        
                    elseif ( ! woocommerce_product_subcategories( array( 'before' => woocommerce_product_loop_start( false ), 'after' => woocommerce_product_loop_end( false ) ) ) ) :

                        wc_get_template( 'loop/no-products-found.php' );

                    endif; ?>
                </div>
            </div>

            <?php if (is_active_sidebar( $right_sidebar ) && $archive_product_style == 'style_2' && (($sidebar == 'right') || ($sidebar == 'both'))) : ?>
                <div class="<?php echo esc_attr($sidebar_col) ?> col-xs-12">
                    <div class="sidebar woocommerce-sidebar sidebar-<?php echo esc_attr($sidebar_width); ?>">
                        <?php dynamic_sidebar( $right_sidebar );?>
                    </div>
                </div>
            <?php endif;?>
            <?php if (($content_col_number != 12) || ($layout_style != 'full')) : ?>
                </div>
            <?php endif;?>

    <?php if ($layout_style != 'full') : ?>
        </div>
    <?php endif; ?>
    <?php
        /**
         * @hooked - yolo_shop_page_content - 5
         **/
        do_action('yolo_after_archive_product_listing');
    ?>
</main>
<?php get_footer( 'shop' ); }?>
