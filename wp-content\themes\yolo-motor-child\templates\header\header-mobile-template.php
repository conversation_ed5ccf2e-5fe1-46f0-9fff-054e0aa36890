<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2016, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

global $yolo_motor_options;
$prefix = 'yolo_';

$header_class = array('yolo-mobile-header');

// Get header mobile layout

$mobile_header_layout = 'header-mobile-1';
if (isset($yolo_motor_options['mobile_header_layout']) && !empty($yolo_motor_options['mobile_header_layout'])) {
	$mobile_header_layout = $yolo_motor_options['mobile_header_layout'];
}

$header_class[] = $mobile_header_layout;

// Get logo url for mobile

if (isset($yolo_motor_options['mobile_header_logo']['url']) && !empty($yolo_motor_options['mobile_header_logo']['url'])) {
	$logo_url = $yolo_motor_options['mobile_header_logo']['url'];
}
else if (isset($yolo_motor_options['logo']['url']) && !empty($yolo_motor_options['logo']['url'])) {
	$logo_url = $yolo_motor_options['logo']['url'];
}

// Get search & mini-cart for header mobile
$mobile_header_shopping_cart = $yolo_motor_options['mobile_header_shopping_cart'];


$mobile_header_search_box = $yolo_motor_options['mobile_header_search_box'];


$mobile_header_menu_drop = 'dropdown';
if (isset($yolo_motor_options['mobile_header_menu_drop']) && !empty($yolo_motor_options['mobile_header_menu_drop'])) {
	$mobile_header_menu_drop = $yolo_motor_options['mobile_header_menu_drop'];
}

$header_container_wrapper_class = array('yolo-header-container-wrapper', 'menu-drop-' . $mobile_header_menu_drop);


$mobile_header_stick = isset($yolo_motor_options['mobile_header_stick']) ? $yolo_motor_options['mobile_header_stick'] : '0';

if ($mobile_header_stick == '1') {
	$header_container_wrapper_class[] = 'header-mobile-sticky';
}

$page_menu = get_post_meta(get_the_ID(),$prefix . 'page_menu_mobile',true);
if (empty($page_menu)) {
	$page_menu = get_post_meta(get_the_ID(),$prefix . 'page_menu',true);
}

$theme_location = 'primary';
if ( has_nav_menu( 'mobile' )) {
	$theme_location = 'mobile';
}
$user = wp_get_current_user();
$header_mobile_nav = array('yolo-mobile-header-nav' , 'menu-drop-' . $mobile_header_menu_drop);

?>
<header id="yolo-mobile-header" class="<?php echo join(' ', $header_class) ?>">
	<?php if ($mobile_header_layout == 'header-mobile-2'): ?>
		<div class="header-mobile-before">
			<a  href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?> - <?php bloginfo( 'description' ); ?>">
				<img src="<?php echo esc_url($logo_url); ?>" alt="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?> - <?php bloginfo( 'description' ); ?>" />
			</a>
		</div>
	<?php endif;?>
	<div class="<?php echo join(' ', $header_container_wrapper_class); ?>">
		<div class="container yolo-mobile-header-wrapper">
			<div class="yolo-mobile-header-inner">
                <?php if (is_user_logged_in()) { ?>
				<div class="toggle-icon-wrapper toggle-mobile-menu" data-ref="yolo-nav-mobile-menu" data-drop-type="<?php echo esc_attr($mobile_header_menu_drop); ?>">
					<div class="toggle-icon"> <span></span></div>
				</div>
                <?php } ?>
				<div class="header-customize">
					<?php if ($mobile_header_search_box == '1'): ?>
						<?php yolo_get_template('header/search-button-mobile'); ?>
					<?php endif; ?>
					<?php if (/* ($mobile_header_shopping_cart == '1') &&  */class_exists( 'WooCommerce' )): ?>
						<?php yolo_get_template('header/mini-cart'); ?>
					<?php endif; ?>
				</div>
				<?php if ($mobile_header_layout != 'header-mobile-2'): ?>
					<div class="header-logo-mobile">
						<a  href="<?php echo esc_url( home_url( '/' ) ); ?>" title="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?> - <?php bloginfo( 'description' ); ?>">
							<img src="<?php echo esc_url($logo_url); ?>" alt="<?php echo esc_attr( get_bloginfo( 'name', 'display' ) ); ?> - <?php bloginfo( 'description' ); ?>" />
                            <span class="logo-slogan">PARTNER <span class="orange">Portal</span></span>
						</a>
					</div>
				<?php endif;?>
			</div>
            <?php if (is_user_logged_in()) { ?>
			<div id="yolo-nav-mobile-menu" class="<?php echo join(' ', $header_mobile_nav) ?>">
                <ul class="dropdown-menu drop-right">
                    <li>
                        <h4>YOUR ACCOUNT</h4>
                    </li>
                    <li><a href="/my-account" class="link link-black black f-12" title="">My Account</a></li><?php
                    if (!in_array('b2b_sales', (array) $user->roles)) {
                    ?>
                    <li><a href="/my-account/orders" class="link link-black black f-12" title="">Orders</a></li>
                    <?php } ?>
                    <li>
                        <h4 class="orange">YOUR LISTS</h4>
                    </li>
                    <li><a href="/my-account/shopping-lists" class="link link-orange orange f-12" title="">Your Shopping Lists</a></li>
                    <li><a href="javascript:;" class="link link-orange orange f-12 create_new mb-1" title="">+ Create a List</a></li>
                    <?php
                    if (in_array('b2b_administrator', (array) $user->roles) || in_array('b2b_customer', (array) $user->roles)) {
                    ?>
                    <li>
                        <hr class="my-1" style="border-color: #E3E3E3 !important;width: calc(100% + 20px);margin: 10px -10px;">
                    </li>
                    <li><a href="/quick-order" class="btn btn-wide w100 orange-bg white f-12" title="">Quick Order</a></li>
                    <?php
                    } 
                    ?>
                    <li>
                        <hr class="my-1" style="border-color: #E3E3E3 !important;width: calc(100% + 20px);margin: 10px -10px;">
                    </li>
                    <li><a href="https://woocommerce-1360393-5064456.cloudwaysapps.com/wp-login.php?action=logout&amp;redirect_to=https%3A%2F%2Fwoocommerce-1176373-4772823.cloudwaysapps.com%2Flogin&amp;_wpnonce=275ba5e474" class="btn btn-wide w100 black-bg white f-12" title="">Log out</a></li>
                </ul>
			</div>
            <?php } ?>
			<?php if ($mobile_header_menu_drop == 'fly'): ?>
				<div class="yolo-mobile-menu-overlay"></div>
			<?php endif;?>
		</div>
	</div>
</header>
