<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

global $yolo_motor_options;

$prefix = 'yolo_';

$header_class = array('yolo-main-header', 'header-1', 'header-desktop-wrapper');
$header_nav_wrapper = array('yolo-header-nav-wrapper');
$header_sticky_effect = '';
// @TODO: need process page_enable_header_customize from header.php
$header_layout_float = get_post_meta(get_the_ID(), $prefix . 'header_layout_float',true);
if ((($header_layout_float === '') || ($header_layout_float == '-1') || is_search() || $header_layout_float === false) && !is_404()) {
	$header_layout_float = $yolo_motor_options['header_1_layout_float'];
}

if ( 1 == $header_layout_float ) {
	$header_class[] = 'header-float';
}

$header_sticky = $yolo_motor_options['header_sticky'];

if ( 1 == $header_sticky ) {
	wp_enqueue_script('sticky-header');
	$header_nav_wrapper[] = 'header-sticky';
    $header_nav_wrapper[] = 'animate';
    $header_sticky_effect = isset($yolo_motor_options['header_sticky_effect']) ? $yolo_motor_options['header_sticky_effect'] : 'slideDown,slideUp';
    $header_sticky_scheme = isset($yolo_motor_options['header_sticky_scheme']) ? $yolo_motor_options['header_sticky_scheme'] : 'inherit';
    $header_nav_wrapper[] = 'sticky-scheme-' . $header_sticky_scheme;
}

$page_menu 				= get_post_meta(get_the_ID(), $prefix . 'page_menu',true);
$header_nav_layout 		= isset($yolo_motor_options['header_1_nav_layout']) ? $yolo_motor_options['header_1_nav_layout'] : '';

if ( 'nav-fullwith' == $header_nav_layout ) {
	$header_nav_wrapper[] = $header_nav_layout;
}
?>

<header id="yolo-header" class="<?php echo join(' ', $header_class) ?>">
	<div class="yolo-header-nav-above text-left">
		<div class="container <?php echo $header_nav_layout; ?>">
			<div class="fl">
				<?php yolo_get_template('header/header-logo' ); ?>
			</div>
			<div class="fr">
				<?php yolo_get_template('header/header-customize-right' ); ?>
			</div>
		</div>
	</div>
	<div class="<?php echo join(' ', $header_nav_wrapper) ?>" data-effect ="<?php echo esc_attr($header_sticky_effect);?>">
		<div class="container">
			<div class="yolo-header-wrapper">
				<div class="header-center">
					<?php if (has_nav_menu('primary')) : ?>
						<div id="primary-menu" class="menu-wrapper">
							<?php
							wp_nav_menu( array(
								'theme_location'  => 'primary',
								'menu'            => $page_menu,
								'container'       => '',
								'menu_class'      => 'yolo-main-menu nav-collapse navbar-nav',
								'menu_id'         => 'main-menu',
								'fallback_cb'     => 'please_set_menu',
								'walker'          => new Yolo_MegaMenu_Walker(),
							) );
							?>
						</div>
					<?php endif; ?>
					<?php yolo_get_template('header/header-customize-nav' ); ?>
				</div>
			</div>
		</div>
	</div>
</header>