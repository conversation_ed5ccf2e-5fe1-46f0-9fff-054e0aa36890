<?php
/**
 *
 * @package    YoloTheme
 * @version    1.0.0
 * @created    25/12/2015
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       https://yolotheme.com
 */
global $post,$yolo_motor_options;
$prefix                         = 'yolo_';
$show_page_title                = get_post_meta(get_the_ID(), $prefix . 'show_page_title', true);
$inherit_single_product_title   = isset($yolo_motor_options['inherit_single_product_title']) ? $yolo_motor_options['inherit_single_product_title'] : 0;
$inherit_single_portfolio_title = isset($yolo_motor_options['inherit_single_portfolio_title']) ? $yolo_motor_options['inherit_single_portfolio_title'] : 1;
$inherit_single_blog_title      = isset($yolo_motor_options['inherit_single_blog_title']) ? $yolo_motor_options['inherit_single_blog_title'] : 0;


/*---------- Add class and css attribute default----------*/
$page_title_warp_class    = array('yolo-page-title-wrap');
$section_page_title_class = array('yolo-page-title-section');

$breadcrumb_class = array('yolo-breadcrumb-wrap s-color');
/*------- Add css Custom <section> -----------*/
$custom_styles = array();
$custom_style  = '';

/*-----Show page title-------*/
if ($show_page_title == '0') {
    return;
}

if (($show_page_title == -1) || ($show_page_title == '')) {
    $show_page_title = $yolo_motor_options['show_page_title'];

    if (is_singular('product')) {
        if ($inherit_single_product_title != 1) {
            $show_page_title = $yolo_motor_options['show_single_product_title'];
        }
    } elseif (is_singular('yolo_portfolio')) {
        if ($inherit_single_portfolio_title != 1) {
            $show_page_title = $yolo_motor_options['show_portfolio_title'];
        }
    } elseif (is_singular('post')) {
        if ($inherit_single_blog_title != 1) {
            $show_page_title = $yolo_motor_options['show_single_blog_title'];
        }
    }

    if ($show_page_title == '') {
        $show_page_title = 1;
    }
}

$page_title_text_align = get_post_meta(get_the_ID(), $prefix . 'page_title_text_align',true);

if(!isset($page_title_text_align) || ($page_title_text_align == '') || ($page_title_text_align == '-1')) {
    $page_title_text_align = $yolo_motor_options['page_title_text_align'];

}

if (!isset($page_title_text_align) || empty($page_title_text_align)) {
    $page_title_text_align = 'left';
}

/*----- Custom Page Title-------*/

$page_title = get_the_title();
if (is_singular('post')) {
    if ($inherit_single_blog_title != 1) {
        $page_title = isset($yolo_motor_options['custom_single_blog_title']) && !empty($yolo_motor_options['custom_single_blog_title']) ? $yolo_motor_options['custom_single_blog_title'] : get_the_title();

        $page_title_text_align = $yolo_motor_options['single_blog_title_text_align'];
        if (!isset($page_title_text_align) || empty($page_title_text_align)) {
            $page_title_text_align = 'left';
        }

    }
}
if (is_singular('product')) {
    if ($inherit_single_product_title != 1) {
        $page_title = isset($yolo_motor_options['custom_single_product_title']) && !empty($yolo_motor_options['custom_single_product_title']) ? $yolo_motor_options['custom_single_product_title'] : get_the_title();

        $page_title_text_align = $yolo_motor_options['single_product_title_text_align'];
        if (!isset($page_title_text_align) || empty($page_title_text_align)) {
            $page_title_text_align = 'left';
        }

    }
}

$page_title_warp_class[] = 'page-title-' . $page_title_text_align;

if (is_404()) {
    $page_title = $yolo_motor_options['page_title_404'];
}

if (get_post_meta(get_the_ID(), $prefix . 'page_title_custom', true)) {
    $page_title = get_post_meta(get_the_ID(), $prefix . 'page_title_custom', true);
}

$page_sub_title = get_post_meta(get_the_ID(), $prefix . 'page_subtitle_custom', true);

if (is_404()) {
    $page_sub_title = $yolo_motor_options['sub_page_title_404'];
}
/*-----Custom page title bg image-------*/
$enable_custom_page_title_bg_image = get_post_meta(get_the_ID(), $prefix . 'enable_custom_page_title_bg_image', true);
$page_title_bg_image_url           = '';

if ($enable_custom_page_title_bg_image == 1) {
    $page_title_bg_image = get_post_meta(get_the_ID(), $prefix . 'page_title_bg_image', true);

    if ($page_title_bg_image) {
        $page_title_bg_image     = wp_get_attachment_url($page_title_bg_image);
        $page_title_bg_image_url = $page_title_bg_image;
    }
} else {
    $page_title_bg_image = $yolo_motor_options['page_title_bg_image'];

    if (is_singular('product')) {
        if ($inherit_single_product_title != 1) {
            $page_title_bg_image = $yolo_motor_options['single_product_title_bg_image'];
        }
    } elseif (is_singular('yolo_portfolio')) {
        if ($inherit_single_portfolio_title != 1) {
            $page_title_bg_image = $yolo_motor_options['portfolio_title_bg_image'];
        }
    } elseif (is_singular('post')) {
        if ($inherit_single_blog_title != 1) {
            $page_title_bg_image = $yolo_motor_options['single_blog_title_bg_image'];
        }
    }

    if (is_404()) {
        $page_title_bg_image = $yolo_motor_options['page_404_bg_image'];
    }

    if (isset($page_title_bg_image) && $page_title_bg_image['url'] != '') {
        $page_title_bg_image_url = $page_title_bg_image['url'];
    } else {
        $page_title_bg_image_url = get_template_directory_uri() . '/assets/images/bg-page-title.png';
    }
}
/* ------Breadcrumbs-----*/
$breadcrumbs_in_page_title = get_post_meta(get_the_ID(), $prefix . 'breadcrumbs_in_page_title', true);

if (($breadcrumbs_in_page_title == -1) || ($breadcrumbs_in_page_title == '')) {
    $breadcrumbs_in_page_title = isset($yolo_motor_options['breadcrumbs_in_page_title']) ? $yolo_motor_options['breadcrumbs_in_page_title'] : 1;
    if (is_singular('product')) {
        if ($inherit_single_product_title != 1) {
            $breadcrumbs_in_page_title = $yolo_motor_options['breadcrumbs_in_single_product_title'];
        }
    } elseif (is_singular('yolo_portfolio')) {
        if ($inherit_single_portfolio_title != 1) {
            $breadcrumbs_in_page_title = $yolo_motor_options['breadcrumbs_in_portfolio_title'];
        }
    }
}

if (is_404()) {
    $breadcrumbs_in_page_title = 0;
}

/*----- Page title height-------*/
$page_title_height = get_post_meta(get_the_ID(), $prefix . 'page_title_height', true);
if (empty($page_title_height)) {
    $page_title_height = $yolo_motor_options['page_title_height']['height'];

    if (is_singular('post')) {
        if ($inherit_single_blog_title != 1) {
            $page_title_height = $yolo_motor_options['single_blog_title_height']['height'];
        }
    }
    if (is_singular('product')) {
        if ($inherit_single_product_title != 1) {
            $page_title_height = $yolo_motor_options['single_product_title_height']['height'];
        }
    }
    if (is_singular('yolo_portfolio')) {
        if ($inherit_single_portfolio_title != 1) {
            $page_title_height = $yolo_motor_options['portfolio_title_height']['height'];
        }
    }
}else{
    $page_title_height = $page_title_height.'px';
}
$custom_styles[] = 'height:' . $page_title_height;
$page_title_bg_color = get_post_meta(get_the_ID(), $prefix . 'page_title_bg_color', true);

if ($page_title_bg_image_url != '') {
    $page_title_warp_class[] = 'page-title-wrap-bg';
    $custom_styles[]         = 'background-image: url(' . $page_title_bg_image_url . ')';
}
if ($page_title_bg_color != '') {
    $custom_styles[] = 'background-color:' . $page_title_bg_color;
}
if ($custom_styles) {
    $custom_style = 'style="' . join(';', $custom_styles) . '"';
}

/*-------- Page title Paralax--------*/
$page_title_parallax = get_post_meta(get_the_ID(), $prefix . 'page_title_parallax', true);

if (!isset($page_title_parallax) || ($page_title_parallax == '') || ($page_title_parallax == '-1')) {
    $page_title_parallax = $yolo_motor_options['page_title_parallax'];

    if (is_singular('product')) {
        if ($inherit_single_product_title != 1) {
            $page_title_parallax = $yolo_motor_options['single_product_title_parallax'];
        }
    } elseif (is_singular('yolo_portfolio')) {
        if ($inherit_single_portfolio_title != 1) {
            $page_title_parallax = $yolo_motor_options['portfolio_title_parallax'];
        }
    } elseif (is_singular('post')) {
        if ($inherit_single_blog_title != 1) {
            $page_title_parallax = $yolo_motor_options['single_blog_title_parallax'];
        }
    }
}


if (!empty($page_title_bg_image_url) && ($page_title_parallax == 1)) {
    wp_enqueue_script('stellar');
    $custom_style .= ' data-stellar-background-ratio="0.5"';
    $page_title_warp_class[] = 'page-title-parallax';
}
/*---------------Page title style--------------*/
$page_title_style = isset($yolo_motor_options['page_title_style']) ? $yolo_motor_options['page_title_style'] : 'page-title-style-1';
/*------- Page title layout--------*/

$page_title_layout = isset($yolo_motor_options['page_title_layout']) ? $yolo_motor_options['page_title_layout'] : 'container-fluid';

if (in_array($page_title_layout, array('container', 'container-fluid'))) {
    $section_page_title_class[] = $page_title_layout;
}
?>
<?php if (( 1 == $show_page_title) || ( 1 == $breadcrumbs_in_page_title)) : ?>
    <div class="<?php echo join(' ', $section_page_title_class) ?> <?php echo esc_attr($page_title_style); ?>">
        <?php if ( 1 == $show_page_title) : ?>
            <section  class="<?php echo join(' ', $page_title_warp_class); ?>" <?php echo wp_kses_post($custom_style); ?>>
                <div class="content-page-title">
                    <div class="container">
                        <div class="page-title-inner block-center">
                            <div class="block-inner">
                                <?php if (is_singular('product')) { ?>
                                        <h2 class="page-title"><?php echo esc_html($page_title); ?></h2>
                                    <?php } else { ?>
                                        <h1 class="page-title"><?php echo esc_html($page_title); ?></h1>
                                    <?php } ?>
                                
                                <?php if ($page_sub_title != ''): ?>
                                    <span class="page-sub-title"><?php echo esc_html($page_sub_title) ?></span>
                                <?php endif;?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        <?php endif;?>
        <?php if ($breadcrumbs_in_page_title == 1): ?>
            <div class="<?php echo join(' ', $breadcrumb_class) ?>">
                <?php yolo_the_breadcrumb();?>
            </div>
        <?php endif;?>
    </div>
<?php endif;?>

