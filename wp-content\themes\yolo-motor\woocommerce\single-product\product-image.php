<?php
/**
 * Single Product Image
 *
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     9.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

global $post, $woocommerce, $product,$yolo_motor_options;
$single_product_show_image_thumb = isset($yolo_motor_options['single_product_show_image_thumb']) ? $yolo_motor_options['single_product_show_image_thumb'] : 0;

$single_product_zoom_image = isset($yolo_motor_options['single_product_zoom_image']) ? $yolo_motor_options['single_product_zoom_image'] : 0;

if ($single_product_zoom_image == 0) {
	$index = 0;
	$product_images = array();
	$image_ids = array();
	$rtl = 'false';
	if(is_rtl()){
		$rtl = 'true';
	}
	if (has_post_thumbnail()) {
		$product_images[$index] = array(
			'image_id' => get_post_thumbnail_id()
		);
		$image_ids[$index] = get_post_thumbnail_id();
		$index++;
	}


	// Additional Images
	$attachment_ids = $product->get_gallery_image_ids();
	if ($attachment_ids) {
		foreach ( $attachment_ids as $attachment_id ) {
			if (in_array($attachment_id,$image_ids)) continue;
			$product_images[$index] = array(
				'image_id' => $attachment_id
			);
			$image_ids[$index] = $attachment_id;
			$index++;
		}
	}


	if ($product->get_type() == 'variable') {
		$available_variations = $product->get_available_variations();
		if (isset($available_variations)){
			foreach ($available_variations as $available_variation){
				$variation_id = $available_variation['variation_id'];
				if (has_post_thumbnail($variation_id)) {
					$variation_image_id = get_post_thumbnail_id($variation_id);

					if (in_array($variation_image_id,$image_ids)) {
						$index_of = array_search($variation_image_id, $image_ids);
						if (isset($product_images[$index_of]['variation_id'])) {
							$product_images[$index_of]['variation_id'] .= $variation_id . '|';
						} else {
							$product_images[$index_of]['variation_id'] = '|' . $variation_id . '|';
						}
						continue;
					}

					$product_images[$index] = array(
						'image_id' => $variation_image_id,
						'variation_id' => '|' . $variation_id . '|'
					);
					$image_ids[$index] = $variation_image_id;
					$index++;
				}
			}
		}
	}
	$attachment_count = count($attachment_ids);
	if ( $attachment_count > 0 ) {
	    $gallery = '[product-gallery]';
	} else {
	    $gallery = '';
	}
	wp_enqueue_style('owl-carousel');
	wp_enqueue_script('owl-carousel');

	wp_enqueue_style('lightGallery');
	wp_enqueue_style('lg-transitions');
	wp_enqueue_style('lightGallery');

	$product_images_thumb = array('product-thumb-wrap');
	$product_images_thumb[] = 'product-image-total-' . $index;
	if ($single_product_show_image_thumb == 0) {
		$product_images_thumb[] = 'product-thumb-disable';
	}
	?>
	<?php if (!empty($product_images)) { ?>
	<div class="single-product-image-inner">
	    <div id="sync1" class="owl-carousel manual lightGallery-js">

		    <?php
		    foreach($product_images as $key => $value) {
			    $index = $key;
			    $image_id = $value['image_id'];
			    $variation_id = isset($value['variation_id']) ? $value['variation_id'] : '' ;
			    $image_title 	= esc_attr( get_the_title( $image_id ) );
			    $image_caption = '';
			    $image_obj = get_post( $image_id );
			    if (isset($image_obj) && isset($image_obj->post_excerpt)) {
				    $image_caption 	= $image_obj->post_excerpt;
			    }
			    $image_link  	= wp_get_attachment_url( $image_id );
			    $image       	= wp_get_attachment_image( $image_id, apply_filters( 'single_product_large_thumbnail_size', 'shop_single' ), array(
				    'title'	=> $image_title,
				    'alt'	=> $image_title
			    ) );
			    echo '<div class="item" data-src="'. $image_link .'">';
			    if (!empty($variation_id)) {
	                echo  apply_filters( 'woocommerce_single_product_image_html', sprintf( '<a href="%s" class="woocommerce-main-image" title="%s" data-variation_id="%s" data-index="%s">%s</a>', $image_link, $image_caption,$variation_id,$index, $image ), $post->ID );
	            } else {
	                echo  apply_filters( 'woocommerce_single_product_image_html', sprintf( '<a href="%s" class="woocommerce-main-image" title="%s" data-index="%s">%s</a>', $image_link, $image_caption,$index, $image ), $post->ID );
	            }
			    echo '</div>';
		    }

		    ?>

	    </div>
		<div class="<?php echo join(' ',$product_images_thumb); ?>">
			<div id="sync2" class="owl-carousel manual">
				<?php
				foreach($product_images as $key => $value) {
					$index = $key;
					$image_id = $value['image_id'];
					$variation_id = isset($value['variation_id']) ? $value['variation_id'] : '' ;
					$image_title 	= esc_attr( get_the_title( $image_id ) );
					$image_caption = '';
					$image_obj = get_post( $image_id );
					if (isset($image_obj) && isset($image_obj->post_excerpt)) {
						$image_caption 	= $image_obj->post_excerpt;
					}


					$image_link  	= wp_get_attachment_url( $image_id );
					$image       	= wp_get_attachment_image( $image_id,  apply_filters( 'single_product_small_thumbnail_size', 'shop_thumbnail' ), array(
						'title'	=> $image_title,
						'alt'	=> $image_title
					) );
					echo '<div class="thumbnail-image">';
					if (!empty($variation_id)) {
						echo  apply_filters( 'woocommerce_single_product_image_thumbnail_html', sprintf( '<a href="javascript:;" itemprop="image" class="woocommerce-thumbnail-image" title="%s" data-variation_id="%s" data-index="%s">%s</a>', $image_caption,$variation_id,$index,  $image ), $post->ID );
					} else {
						echo  apply_filters( 'woocommerce_single_product_image_thumbnail_html', sprintf( '<a href="javascript:;" itemprop="image" class="woocommerce-thumbnail-image" title="%s" data-index="%s">%s</a>', $image_caption,$index , $image), $post->ID );
					}
					echo '</div>';
				}

				?>
			</div>
		</div>
	</div>
	<?php } else {
			$html  = '<div class="woocommerce-product-gallery__image--placeholder">';
			$html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src( 'woocommerce_single' ) ), esc_html__( 'Awaiting product image', 'yolo-motor' ) );
			$html .= '</div>';
			echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html ); // phpcs:disable 
	}
	?>
	<script type="text/javascript">
		(function($) {
			"use strict";
			$(document).ready(function() {
			// http://poligon.pro/owl/
			// https://github.com/OwlCarousel2/OwlCarousel2/issues/80
			// https://codepen.io/washaweb/pen/KVRxRW

				var sync1    = $("#sync1",".single-product-image-inner");
				var sync2    = $("#sync2",".single-product-image-inner");
				var flag     = false;
				var duration = 500;


				sync1
					.owlCarousel({
						items: 1,
						margin: 0,
						rtl : <?php echo esc_js($rtl);?>,
						nav: true,
						autoHeight : true,
						navText: ["<i class='fa fa-angle-left'></i>","<i class='fa fa-angle-right'></i>"],
						dots: true
					})
					.on('changed.owl.carousel', function (e) {
						if (!flag) {
							flag = true;
							sync2.trigger('to.owl.carousel', [e.item.index, duration, true]);
							flag = false;
						}

						// Add class synced to current slide
						var current = e.item.index;
						$("#sync2")
							.find(".owl-item")
							.removeClass("synced")
							.eq(current)
							.addClass("synced");
					});

				sync2
					.owlCarousel({
						margin: 0,
						rtl : <?php echo esc_js($rtl);?>,
						items: 4,
						nav: true,
						navText: ["<i class='fa fa-angle-left'></i>","<i class='fa fa-angle-right'></i>"],
						center: false,
						dots: true,
						onInitialized : function(){
							sync2.find(".owl-item").eq(0).addClass("synced");
						}
					})
					.on('click', '.owl-item', function () {
						sync1.trigger('to.owl.carousel', [$(this).index(), duration, true]);
					})
					.on('changed.owl.carousel', function (e) {
						if (!flag) {
							flag = true;		
							sync1.trigger('to.owl.carousel', [e.item.index, duration, true]);
							flag = false;
						}
					});

				$(document).on('change','.variations_form .variations select,.variations_form .variation_form_section select,div.select',function(){
					var variation_form = $(this).closest( '.variations_form' );
					var current_settings = {},
						reset_variations = variation_form.find( '.reset_variations' );
					variation_form.find('.variations select,.variation_form_section select' ).each( function() {
						// Encode entities
						var value = $(this ).val();

						// Add to settings array
						current_settings[ $( this ).attr( 'name' ) ] = jQuery(this ).val();
					});

					variation_form.find('.variation_form_section div.select input[type="hidden"]' ).each( function() {
						// Encode entities
						var value = $(this ).val();

						// Add to settings array
						current_settings[ $( this ).attr( 'name' ) ] = jQuery(this ).val();
					});

					var all_variations = variation_form.data( 'product_variations' );

					var variation_id = 0;
					var match = true;

					for (var i = 0; i < all_variations.length; i++)
					{
						match = true;
						var variations_attributes = all_variations[i]['attributes'];
						for(var attr_name in variations_attributes) {
							var val1 = variations_attributes[attr_name];
							var val2 = current_settings[attr_name];
							if (val1 == undefined || val2 == undefined ) {
								match = false;
								break;
							}
							if (val1.length == 0) {
								continue;
							}

							if (val1 != val2) {
								match = false;
								break;
							}
						}
						if (match) {
							variation_id = all_variations[i]['variation_id'];
							break;
						}
					}

					if (variation_id > 0) {
						var index = parseInt($('a[data-variation_id*="|'+variation_id+'|"]','#sync1').data('index'),10) ;
						if (!isNaN(index) ) {
							sync1.trigger('to.owl.carousel', [index, duration, true]);
						}
					}
				});

			});
		})(jQuery);
	</script>
	<?php 
} else {
	
	if ( ! function_exists( 'wc_get_gallery_image_html' ) ) {
		return;
	}

	$columns           = apply_filters( 'woocommerce_product_thumbnails_columns', 4 );
	$post_thumbnail_id = $product->get_image_id();
	$wrapper_classes   = apply_filters(
		'woocommerce_single_product_image_gallery_classes',
		array(
			'woocommerce-product-gallery',
			'woocommerce-product-gallery--' . ( $post_thumbnail_id ? 'with-images' : 'without-images' ),
			'woocommerce-product-gallery--columns-' . absint( $columns ),
			'images',
		)
	);
	?>
	<div class="<?php echo esc_attr( implode( ' ', array_map( 'sanitize_html_class', $wrapper_classes ) ) ); ?>" data-columns="<?php echo esc_attr( $columns ); ?>" style="opacity: 0; transition: opacity .25s ease-in-out;">
		<figure class="woocommerce-product-gallery__wrapper">
			<?php
			if ( $post_thumbnail_id ) {
				$html = wc_get_gallery_image_html( $post_thumbnail_id, true );
			} else {
				$html  = '<div class="woocommerce-product-gallery__image--placeholder">';
				$html .= sprintf( '<img src="%s" alt="%s" class="wp-post-image" />', esc_url( wc_placeholder_img_src( 'woocommerce_single' ) ), esc_html__( 'Awaiting product image', 'woocommerce' ) );
				$html .= '</div>';
			}

			echo apply_filters( 'woocommerce_single_product_image_thumbnail_html', $html, $post_thumbnail_id ); // phpcs:disable WordPress.XSS.EscapeOutput.OutputNotEscaped

			do_action( 'woocommerce_product_thumbnails' );
			?>
		</figure>
	</div>

<?php 
}