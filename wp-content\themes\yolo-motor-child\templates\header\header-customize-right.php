<?php
/**
 *
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

global $yolo_header_customize_current,$yolo_motor_options;
$yolo_header_layout = yolo_get_header_layout();

$prefix = 'yolo_';
$yolo_header_customize_current = 'right';

$header_customize_class = array('header-customize header-customize-right');

$header_customize = array();
if ('header-1' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_1_customize_right']) && isset($yolo_motor_options['header_1_customize_right']['enabled']) && is_array($yolo_motor_options['header_1_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_1_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

if ('header-2' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_2_customize_right']) && isset($yolo_motor_options['header_2_customize_right']['enabled']) && is_array($yolo_motor_options['header_2_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_2_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

if ('header-3' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_3_customize_right']) && isset($yolo_motor_options['header_3_customize_right']['enabled']) && is_array($yolo_motor_options['header_3_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_3_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

if ('header-4' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_4_customize_right']) && isset($yolo_motor_options['header_4_customize_right']['enabled']) && is_array($yolo_motor_options['header_4_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_4_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

if ('header-5' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_5_customize_right']) && isset($yolo_motor_options['header_5_customize_right']['enabled']) && is_array($yolo_motor_options['header_5_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_5_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}
if ('header-6' == $yolo_header_layout) {
    if (isset($yolo_motor_options['header_6_customize_right']) && isset($yolo_motor_options['header_6_customize_right']['enabled']) && is_array($yolo_motor_options['header_6_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['header_6_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

if ('header-sidebar' == $yolo_header_layout) {
    if (isset($yolo_motor_options['headersidebar_customize_right']) && isset($yolo_motor_options['headersidebar_customize_right']['enabled']) && is_array($yolo_motor_options['headersidebar_customize_right']['enabled'])) {
        foreach ($yolo_motor_options['headersidebar_customize_right']['enabled'] as $key => $value) {
            $header_customize[] = $key;
        }
    }
}

?>
<?php if (count($header_customize) > 1): ?>
	<div class="<?php echo join(' ', $header_customize_class) ?> pt-0">

        <?php $account_rendered = false; ?>

		<?php foreach ($header_customize as $key){
			switch ($key) {
				case 'search-button':
					yolo_get_template('header/search-button');
					break;
				case 'search-box':
					yolo_get_template('header/search-box');
					break;
				case 'search-with-category':
					yolo_get_template('header/search-with-category');
					break;
				case 'shopping-cart':
					if (class_exists( 'WooCommerce' )) {
						echo '<a href="/quick-order" class="btn btn-wide w100 orange-bg white f-12" title="">QUICK ORDER</a>';
						yolo_get_template('header/mini-cart');
						yolo_get_template('header/account');
						$account_rendered = true;
					}
					break;
				case 'shopping-cart-price':
					if (class_exists( 'WooCommerce' )) {
						yolo_get_template('header/mini-cart-price');
					}
					break;
				case 'wishlist':
					if (class_exists( 'WooCommerce' ) && class_exists('YITH_WCWL')) {
						yolo_get_template('header/wishlist');
					}
					break;
				case 'social-profile':
					yolo_get_template('header/social-profile');
					break;
				case 'custom-text':
					yolo_get_template('header/custom-text');
					break;
				case 'canvas-menu':
					yolo_get_template('header/canvas-menu');
					break;

			}
		} ?>
		<?php if (!$account_rendered) { yolo_get_template('header/account'); } ?>
	</div>
<?php endif;?>