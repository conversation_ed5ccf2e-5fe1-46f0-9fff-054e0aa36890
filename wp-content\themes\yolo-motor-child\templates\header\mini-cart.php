<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

global $yolo_motor_options;
$prefix = 'yolo_';

$icon_shopping_cart_class = array('shopping-cart-wrapper', 'header-customize-item', 'no-price' , 'ml-2');
if ($yolo_motor_options['mobile_header_shopping_cart'] == '0') {
	$icon_shopping_cart_class[] = 'mobile-hide-shopping-cart';
}
$icon_shopping_cart_class[] = 'style-default';
if(is_user_logged_in()){

?>
<div class="<?php echo join(' ', $icon_shopping_cart_class); ?>">
	<a href="/cart">
        <div class="widget_shopping_cart_content">
            <?php yolo_get_template('woocommerce/cart/mini-cart'); ?>
        </div>
	</a>
</div>
<?php 
}
?>