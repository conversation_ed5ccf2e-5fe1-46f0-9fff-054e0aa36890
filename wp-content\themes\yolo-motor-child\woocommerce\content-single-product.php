<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * Override this template by copying it to yourtheme/woocommerce/content-single-product.php
 *
 * <AUTHOR>
 * @package 	WooCommerce/Templates
 * @version     3.6.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}
global $product;
?>

<?php
	/**
	 * woocommerce_before_single_product hook
	 *
	 * @hooked wc_print_notices - 10
	 */
	 do_action( 'woocommerce_before_single_product' );

	 if ( post_password_required() ) {
	 	echo get_the_password_form();
	 	return;
	 }
?>

<div id="product-<?php the_ID(); ?>" <?php wc_product_class( '', $product ); ?>>

    <div class="single-product-info clearfix">
        <div class="single-product-image-wrap">
            <div class="single-product-image">
                <?php
                /**
                 * woocommerce_before_single_product_summary hook
                 *
                 * @hooked woocommerce_show_product_sale_flash - 10
                 * @hooked woocommerce_show_product_images - 20
                 */
                do_action( 'woocommerce_before_single_product_summary' );
                $CE = get_post_meta( get_the_ID(), '_ce_approved', true );
                ?>
            </div>
            <span class="ce_approved_badge"><?php echo $CE == 'Yes' ? '<img src="' . get_stylesheet_directory_uri() . '/assets/images/logo-ce.png' . '" alt="" class="img-fluid" style="width: 40px;" />' : ''; ?></span>
        </div>

        <div class="summary-product-wrap">
            <div class="summary-product entry-summary">
                <?php
                /**
                 * woocommerce_single_product_summary hook
                 *
                 * @hooked woocommerce_template_single_title - 5
                 * @hooked woocommerce_template_single_rating - 10
                 * @hooked woocommerce_template_single_price - 10
                 * @hooked woocommerce_template_single_excerpt - 20
                 * @hooked woocommerce_template_single_add_to_cart - 30
                 * @hooked yolo_woocommerce_template_single_function - 35
                 * @hooked woocommerce_template_single_meta - 40
                 * @hooked woocommerce_template_single_sharing - 50
                 */
                do_action( 'woocommerce_single_product_summary' );
                ?>
            </div>
        </div>
    </div>

	<?php
		/**
		 * woocommerce_after_single_product_summary hook
		 *
		 * @hooked woocommerce_output_product_data_tabs - 10
		 * @hooked woocommerce_upsell_display - 15
		 * @hooked woocommerce_output_related_products - 20
		 */
        remove_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
		// do_action( 'woocommerce_after_single_product_summary' );
        // add_action( 'woocommerce_after_single_product_summary', 'woocommerce_output_related_products', 20 );
	?>

	<meta itemprop="url" content="<?php the_permalink(); ?>" />

</div><!-- #product-<?php the_ID(); ?> -->

<?php // do_action( 'woocommerce_after_single_product' ); ?>
