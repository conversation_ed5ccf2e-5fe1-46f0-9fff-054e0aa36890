<?php
/**
 * My Account Dashboard
 *
 * Shows the first intro screen on the account dashboard.
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/myaccount/dashboard.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 4.4.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly.
}

$allowed_html = array(
	'a' => array(
		'href' => array(),
	),
);
?>

<p>
	<?php
	printf(
		/* translators: 1: user display name 2: logout url */
		wp_kses( __( 'Hello %1$s (not %1$s? <a href="%2$s">Log out</a>)', 'woocommerce' ), $allowed_html ),
		'<strong>' . esc_html( $current_user->display_name ) . '</strong>',
		esc_url( wc_logout_url() )
	);
	?>
</p>

<p>
	<?php
	/* translators: 1: Orders URL 2: Address URL 3: Account URL. */
	$dashboard_desc = __( 'From your account dashboard you can view your <a href="%1$s">recent orders</a>, manage your <a href="%2$s">billing address</a>, and <a href="%3$s">edit your password and account details</a>.', 'woocommerce' );
	if ( wc_shipping_enabled() ) {
		/* translators: 1: Orders URL 2: Addresses URL 3: Account URL. */
		$dashboard_desc = __( 'From your account dashboard you can view your <a href="%1$s">recent orders</a>, manage your <a href="%2$s">shipping and billing addresses</a>, and <a href="%3$s">edit your password and account details</a>.', 'woocommerce' );
	}
	printf(
		wp_kses( $dashboard_desc, $allowed_html ),
		esc_url( wc_get_endpoint_url( 'orders' ) ),
		esc_url( wc_get_endpoint_url( 'edit-address' ) ),
		esc_url( wc_get_endpoint_url( 'edit-account' ) )
	);
	?>
</p>
<?php
	/**
	 * My Account dashboard.
	 *
	 * @since 2.6.0
	 */
	do_action( 'woocommerce_account_dashboard' );

	/**
	 * Deprecated woocommerce_before_my_account action.
	 *
	 * @deprecated 2.6.0
	 */
	do_action( 'woocommerce_before_my_account' );

	/**
	 * Deprecated woocommerce_after_my_account action.
	 *
	 * @deprecated 2.6.0
	 */
	do_action( 'woocommerce_after_my_account' );

/* Omit closing PHP tag at the end of PHP files to avoid "headers already sent" issues. */


$user = wp_get_current_user();
$main_user_id = get_main_b2b_admin_id($user->ID);
$company_code = get_user_meta($main_user_id, '_companycode', true);
if($company_code == "3090"){
    ?>
    <div class="dash-text">
        <p>For any assistance, please feel free to reach out to us:</p>
        <p class="mb-0">Customer Service: Call us at <a href="tel:+***********" class="orange">****** 541 1418</a> for any general inquiries or support.</p>
        <p class="mb-0">Technical Support: For technical support or product selection, our team is available at <a href="tel:+***********" class="orange">****** 477 8326</a> to assist you.</p>
        <p>If you prefer email, you can also contact us at <a href="mailto:<EMAIL>" class="orange"><EMAIL></a>, and we’ll respond as quickly as possible.</p>
        <p>We're here to help!</p>
    </div>
<?php }else{ ?>
    <div class="dash-text">
        <p>For any assistance, please feel free to reach out to us:</p>
        <p class="mb-0">Customer Service: Call us at <a href="+31455678877" class="orange">+31 45 567 8877</a> for any general inquiries or support.</p>
        <p class="mb-0">Technical Support: For technical support or product selection, our team is available at <a href="tel:+31455678877" class="orange">+31 45 567 8877</a> to assist you.</p>
        <p>If you prefer email, you can also contact us at <a href="mailto:<EMAIL>" class="orange"><EMAIL></a>, and we’ll respond as quickly as possible.</p>
        <p>We're here to help!</p>
    </div>
<?php } ?>
