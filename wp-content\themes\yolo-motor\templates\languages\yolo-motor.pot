#, fuzzy
msgid ""
msgstr ""
"Plural-Forms: nplurals=INTEGER; plural=EXPRESSION;\n"
"Project-Id-Version: Yolo Motor\n"
"POT-Creation-Date: 2022-10-24 08:57+0700\n"
"PO-Revision-Date: 2022-10-24 08:57+0700\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: style.css\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"

#: framework/core/functions/action.php:57
#: framework/core/functions/action.php:138
msgid "View More"
msgstr ""

#: framework/core/functions/action.php:79
#: framework/core/functions/action.php:159
msgid ""
"Sorry, but nothing matched your search criteria. Please try again with some "
"different keywords."
msgstr ""

#: framework/core/functions/action.php:285
msgid "Contact"
msgstr ""

#: framework/core/functions/action.php:338
msgid "Specialty"
msgstr ""

#: framework/core/functions/action.php:346
msgid "Certificates"
msgstr ""

#: framework/core/functions/base.php:324
msgid "Login / Register"
msgstr ""

#: framework/core/functions/base.php:352
msgid "My Account"
msgstr ""

#: framework/core/functions/base.php:355
msgid "Edit Profile"
msgstr ""

#: framework/core/functions/base.php:358
msgid "Logout"
msgstr ""

#: framework/core/functions/blog.php:20
msgid "Format"
msgstr ""

#: framework/core/functions/blog.php:79
#: framework/core/functions/woocommerce.php:1014
msgid "Loading..."
msgstr ""

#: framework/core/functions/blog.php:80
#: framework/core/functions/woocommerce.php:1015
#: framework/includes/options-config.php:698
msgid "Load More"
msgstr ""

#: framework/core/functions/blog.php:551
msgid "play"
msgstr ""

#: framework/core/functions/blog.php:552
msgid "pause"
msgstr ""

#: framework/core/functions/blog.php:563
msgid "mute"
msgstr ""

#: framework/core/functions/blog.php:566
msgid "unmute"
msgstr ""

#: framework/core/functions/blog.php:581
msgid "shuffle"
msgstr ""

#: framework/core/functions/blog.php:584
msgid "shuffle off"
msgstr ""

#: framework/core/functions/blog.php:587
msgid "repeat"
msgstr ""

#: framework/core/functions/blog.php:590
msgid "repeat off"
msgstr ""

#: framework/core/functions/blog.php:596
#, php-format
msgid ""
"<span>Update Required</span> To play the media you will need to either "
"update your browser to a recent version or update your <a href=\"%s\" target="
"\"_blank\">Flash plugin</a>."
msgstr ""

#: framework/core/functions/blog.php:663 templates/content-page.php:18
msgid "Pages:"
msgstr ""

#: framework/core/functions/blog.php:705
#, php-format
msgid "%s"
msgstr ""

#: framework/core/functions/blog.php:708
#, php-format
msgid "%1$s ago"
msgstr ""

#: framework/core/functions/blog.php:715
msgid "Your comment is awaiting moderation."
msgstr ""

#: framework/core/functions/blog.php:720
msgid "Edit"
msgstr ""

#: framework/core/functions/blog.php:750
msgid "Enter Your Name*"
msgstr ""

#: framework/core/functions/blog.php:751
msgid "Enter Your Email*"
msgstr ""

#: framework/core/functions/blog.php:752 framework/core/functions/blog.php:767
msgid "Enter Your Comment"
msgstr ""

#: framework/core/functions/blog.php:756
#, php-format
msgid ""
"Logged in as <a href=\"%1$s\">%2$s</a>. <a href=\"%3$s\" title=\"Log out of "
"this account\">Log out?</a>"
msgstr ""

#: framework/core/functions/blog.php:757
msgid "Leave your thought"
msgstr ""

#: framework/core/functions/blog.php:758
#, php-format
msgid "Leave a reply to %s"
msgstr ""

#: framework/core/functions/blog.php:759
msgid "Click here to cancel the reply"
msgstr ""

#: framework/core/functions/blog.php:762
msgid "Post Comments"
msgstr ""

#: framework/core/functions/breadcrumb.php:48
#: framework/core/functions/breadcrumb.php:53 templates/breadcrumb.php:18
msgid "Home"
msgstr ""

#: framework/core/functions/breadcrumb.php:118
#: framework/core/functions/breadcrumb.php:121
#: framework/core/functions/breadcrumb.php:124
msgid "Archives for "
msgstr ""

#: framework/core/functions/breadcrumb.php:126
msgid "Archives by: "
msgstr ""

#: framework/core/functions/breadcrumb.php:130
msgid "Search results for \""
msgstr ""

#: framework/core/functions/breadcrumb.php:134
msgid "Page Not Found"
msgstr ""

#: framework/core/functions/filter.php:19
msgid "Search Here..."
msgstr ""

#: framework/core/functions/generate-less.php:39
msgid "Could not save file"
msgstr ""

#: framework/core/functions/resize-image.php:43
msgid "No image URL has been entered."
msgstr ""

#: framework/core/functions/resize-image.php:82
msgid "Image is BMP. Please use either JPG or PNG."
msgstr ""

#: framework/core/functions/woocommerce.php:46
msgid "Sale"
msgstr ""

#: framework/core/functions/woocommerce.php:81
#: woocommerce/quick-view/rating.php:29
#: woocommerce/single-product/rating.php:27
#, php-format
msgid "Rated %s out of 5"
msgstr ""

#: framework/core/functions/woocommerce.php:88
msgid "out of 5"
msgstr ""

#: framework/core/functions/woocommerce.php:144
#: framework/includes/options-config.php:1404
#: framework/includes/options-config.php:1460
#: framework/includes/options-config.php:1597
#: framework/includes/options-config.php:1665
#: framework/includes/options-config.php:1721
#: framework/includes/options-config.php:1860
#: framework/includes/options-config.php:1911
#: framework/includes/options-config.php:1981
#: framework/includes/options-config.php:2122
#: framework/includes/options-config.php:2179
#: framework/includes/options-config.php:2320
#: framework/includes/options-config.php:2374
#: framework/includes/options-config.php:2514
#: framework/includes/options-config.php:2570
#: framework/includes/options-config.php:2665
#: framework/includes/options-config.php:2714
msgid "Wishlist"
msgstr ""

#: framework/core/functions/woocommerce.php:171
msgid "Color"
msgstr ""

#: framework/core/functions/woocommerce.php:172
#: framework/includes/meta-boxes.php:48
msgid "Image"
msgstr ""

#: framework/core/functions/woocommerce.php:173
msgid "Label"
msgstr ""

#: framework/core/functions/woocommerce.php:214
msgid "Select terms"
msgstr ""

#: framework/core/functions/woocommerce.php:226
msgid "Select all"
msgstr ""

#: framework/core/functions/woocommerce.php:227
msgid "Select none"
msgstr ""

#: framework/core/functions/woocommerce.php:228
msgid "Add new"
msgstr ""

#: framework/core/functions/woocommerce.php:260
#: framework/core/functions/woocommerce.php:344
#: framework/core/functions/woocommerce.php:429
msgid "Choose an option"
msgstr ""

#: framework/core/functions/woocommerce.php:734
msgid "Continue shopping"
msgstr ""

#: framework/core/functions/woocommerce.php:871
msgid "Product New"
msgstr ""

#: framework/core/functions/woocommerce.php:877
msgid "Product Hot"
msgstr ""

#: framework/core/functions/woocommerce.php:900
#: woocommerce/loop/sale-flash.php:25
#: woocommerce/single-product/sale-flash.php:24
msgid "New"
msgstr ""

#: framework/core/functions/woocommerce.php:901
#: woocommerce/loop/sale-flash.php:29
#: woocommerce/single-product/sale-flash.php:28
msgid "Hot"
msgstr ""

#: framework/core/functions/woocommerce.php:1075
msgid "Prev"
msgstr ""

#: framework/core/functions/woocommerce.php:1076
msgid "Next"
msgstr ""

#: framework/core/functions/woocommerce.php:1121
msgid "No product categories exist."
msgstr ""

#: framework/core/functions/woocommerce.php:1126
#: templates/header/search-with-category.php:23
msgid "Categories"
msgstr ""

#: framework/core/functions/woocommerce.php:1128
#: framework/core/meta-box/inc/fields/select.php:89
#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:125
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:191
msgid "All"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:84
#, php-format
msgid "%s (Invalid)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:88
#, php-format
msgid "%s (Pending)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:111
msgid "Move up"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:124
msgid "Move down"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:126
#: framework/core/megamenu/edit_custom_walker.php:128
msgid "Edit Menu Item"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:137
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:153
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:207
msgid "URL"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:144
msgid "Navigation Label"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:150
msgid "Title Attribute"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:157
msgid "Open link in a new window/tab"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:162
msgid "CSS Classes (optional)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:168
msgid "Link Relationship (XFN)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:174
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:149
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:204
msgid "Description"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:176
msgid ""
"The description will be displayed in the menu if the current theme supports "
"it."
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:186
msgid "Sub Label"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:192
msgid "Sub Label Color"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:198
msgid "Icon"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:214
msgid "Ex: balance-scale"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:222
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:433
msgid "Size"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:229
msgid "Icon Alignment"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:231
#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:159
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:236
#: framework/includes/meta-boxes.php:676
#: framework/includes/options-config.php:711
#: framework/includes/options-config.php:911
msgid "Left"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:232
#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:145
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:222
#: framework/includes/meta-boxes.php:678
#: framework/includes/options-config.php:713
#: framework/includes/options-config.php:913
msgid "Right"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:233
#: framework/includes/meta-boxes.php:677
#: framework/includes/options-config.php:712
#: framework/includes/options-config.php:912
msgid "Center"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:246
msgid "Enable megamenu"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:264
msgid "Megamenu style"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:267
msgid "Column"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:268
msgid "Tab"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:275
msgid "Megamenu columns"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:278
#: framework/core/megamenu/edit_custom_walker.php:335
msgid "Two"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:279
#: framework/core/megamenu/edit_custom_walker.php:336
msgid "Three"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:280
#: framework/core/megamenu/edit_custom_walker.php:337
msgid "Four"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:281
msgid "Five"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:282
msgid "Six"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:290
#: framework/includes/meta-boxes.php:620
msgid "Background Image"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:300
msgid "Sub menu fullwidth? (only use with Column style)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:308
msgid "Hide Mega menu heading?"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:315
msgid "Mega Menu Widget Area"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:317
msgid "Select Widget Area"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:331
msgid "Tab columns (only use with tab style)"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:334
msgid "One"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:352
#, php-format
msgid "Original: %s"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:365
#: framework/core/meta-box/inc/fields/file-input.php:40
#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:321
#: framework/core/redux-framework/ReduxCore/inc/fields/media/field_media.php:205
#: framework/core/redux-framework/ReduxCore/inc/fields/multi_text/field_multi_text.php:69
#: framework/core/redux-framework/ReduxCore/inc/fields/multi_text/field_multi_text.php:73
#: framework/core/redux-framework/ReduxCore/inc/fields/multi_text/field_multi_text.php:76
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:133
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:190
#: framework/core/tax-meta-class/tax-meta-class.php:373
#: framework/core/tax-meta-class/tax-meta-class.php:420
msgid "Remove"
msgstr ""

#: framework/core/megamenu/edit_custom_walker.php:366
msgid "Cancel"
msgstr ""

#: framework/core/megamenu/megamenu.php:284
msgid "No menu assigned"
msgstr ""

#: framework/core/meta-box/inc/clone.php:97
msgid "+ Add more"
msgstr ""

#: framework/core/meta-box/inc/core.php:41
msgid "Documentation"
msgstr ""

#: framework/core/meta-box/inc/core.php:42
msgid "Extensions"
msgstr ""

#: framework/core/meta-box/inc/fields/autocomplete.php:20
#: framework/core/meta-box/inc/fields/autocomplete.php:79
#: framework/core/meta-box/inc/fields/autocomplete.php:94
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:167
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:220
msgid "Delete"
msgstr ""

#: framework/core/meta-box/inc/fields/button.php:32
msgid "Click me"
msgstr ""

#: framework/core/meta-box/inc/fields/checkbox-advanced.php:81
#: framework/core/meta-box/inc/fields/checkbox.php:67
msgid "Yes"
msgstr ""

#: framework/core/meta-box/inc/fields/checkbox-advanced.php:81
#: framework/core/meta-box/inc/fields/checkbox.php:67
#: framework/vc_extension/update_params.php:24
msgid "No"
msgstr ""

#: framework/core/meta-box/inc/fields/file-input.php:19
msgid "Select File"
msgstr ""

#: framework/core/meta-box/inc/fields/file-input.php:38
#: framework/core/meta-box/inc/fields/select.php:89
msgid "Select"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:21
#, php-format
msgid "You may only upload maximum %d file"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:23
#, php-format
msgid "You may only upload maximum %d files"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:79
msgid "Error: Cannot delete file"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:91
msgctxt "file upload"
msgid "Upload Files"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:92
msgctxt "file upload"
msgid "+ Add new file"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:156
msgctxt "file upload"
msgid "Delete"
msgstr ""

#: framework/core/meta-box/inc/fields/file.php:157
msgctxt "file upload"
msgid "Edit"
msgstr ""

#: framework/core/meta-box/inc/fields/footer.php:59
#: framework/core/meta-box/inc/fields/product-block.php:59
#: framework/core/redux-framework/ReduxCore/framework.php:1882
#: framework/includes/meta-boxes.php:159 framework/includes/meta-boxes.php:172
#: framework/includes/meta-boxes.php:229 framework/includes/meta-boxes.php:260
#: framework/includes/meta-boxes.php:271 framework/includes/meta-boxes.php:416
#: framework/includes/meta-boxes.php:528 framework/includes/meta-boxes.php:541
#: framework/includes/meta-boxes.php:675 framework/includes/meta-boxes.php:690
#: framework/includes/meta-boxes.php:715
#: framework/includes/options-config.php:697
#: framework/includes/options-config.php:1235
#: framework/includes/options-config.php:3138
msgid "Default"
msgstr ""

#: framework/core/meta-box/inc/fields/key-value.php:126
msgid "Key"
msgstr ""

#: framework/core/meta-box/inc/fields/key-value.php:127
msgid "Value"
msgstr ""

#: framework/core/meta-box/inc/fields/map.php:61
msgid "Find Address"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:21
msgctxt "media"
msgid "+ Add Media"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:22
msgctxt "media"
msgid " file"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:23
msgctxt "media"
msgid " files"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:24
msgctxt "media"
msgid "Remove"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:25
msgctxt "media"
msgid "Edit"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:26
msgctxt "media"
msgid "View"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:27
msgctxt "media"
msgid "No Title"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:30
msgctxt "media"
msgid "Select Files"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:31
msgctxt "media"
msgid "or"
msgstr ""

#: framework/core/meta-box/inc/fields/media.php:32
msgctxt "media"
msgid "Drop files here to upload"
msgstr ""

#: framework/core/meta-box/inc/fields/oembed.php:64
msgid "Embed HTML not available."
msgstr ""

#: framework/core/meta-box/inc/fields/oembed.php:79
msgid "Preview"
msgstr ""

#: framework/core/meta-box/inc/fields/post.php:36
msgid "Select a post"
msgstr ""

#: framework/core/meta-box/inc/fields/post.php:41
#: framework/core/meta-box/inc/fields/taxonomy.php:52
#, php-format
msgid "Select a %s"
msgstr ""

#: framework/core/meta-box/inc/fields/select-advanced.php:46
#: framework/core/option-extensions/extensions/footer/footer/field_footer.php:102
#: framework/core/option-extensions/extensions/menu/menu/field_menu.php:93
#: framework/core/redux-framework/ReduxCore/inc/fields/select/field_select.php:83
#: framework/core/redux-framework/ReduxCore/inc/fields/select_image/field_select_image.php:63
msgid "Select an item"
msgstr ""

#: framework/core/meta-box/inc/fields/select.php:89
msgid "None"
msgstr ""

#: framework/core/meta-box/inc/fields/taxonomy.php:47
msgid "Select a term"
msgstr ""

#: framework/core/meta-box/inc/fields/thickbox-image.php:55
msgctxt "image upload"
msgid "Upload Images"
msgstr ""

#: framework/core/meta-box/inc/fields/user.php:30
msgid "Select an user"
msgstr ""

#: framework/core/meta-box/inc/validation.php:47
msgid "Please correct the errors highlighted below and try again."
msgstr ""

#: framework/core/option-extensions/extensions/color_alpha/color_alpha/field_color_alpha.php:46
#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:124
#: framework/core/redux-framework/ReduxCore/inc/fields/color/field_color.php:73
#: framework/core/redux-framework/ReduxCore/inc/fields/color_gradient/field_color_gradient.php:79
#: framework/core/redux-framework/ReduxCore/inc/fields/color_gradient/field_color_gradient.php:91
msgid "Transparent"
msgstr ""

#: framework/core/option-extensions/extensions/footer/footer/field_footer.php:126
msgid ""
"No items of this type were found. Please activate Yolo Motor Framework "
"plugin and create footer at Footer Block"
msgstr ""

#: framework/core/option-extensions/extensions/menu/menu/field_menu.php:118
#: framework/core/redux-framework/ReduxCore/inc/fields/select/field_select.php:134
#: framework/core/redux-framework/ReduxCore/inc/fields/select_image/field_select_image.php:144
msgid "No items of this type were found."
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:454
#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:682
msgid "You have changes that are not saved. Would you like to save them now?"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:462
#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:683
msgid "Are you sure? Resetting will lose all custom values."
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:470
msgid "Are you sure? Resetting will lose all custom values in this section."
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:478
#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:684
msgid ""
"Your current options will be replaced with the values of this preset. Would "
"you like to proceed?"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:485
msgid "Please Wait"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:496
msgid "There was an error saving. Here is the result of your action:"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/enqueue.php:497
msgid ""
"There was a problem with your action. Please try again or reload the page."
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:65
msgid "Warning- This options panel will not work properly without javascript!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:141
msgid "Settings Imported!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:156
msgid "All Defaults Restored!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:170
msgid "Section Defaults Restored!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:184
msgid "Settings Saved!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:204
msgid "Settings have changed, you should save them!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:212
msgid "error(s) were found!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:220
msgid "warning(s) were found!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/core/panel.php:312
msgid ""
"<strong>Your panel has bundled outdated copies of Redux Framework template "
"files</strong> &#8211; if you encounter functionality issues this could be "
"the reason. Ensure you update or remove them."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:220
#, php-format
msgid "Options panel created using %1$s"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:220
msgid "Redux Framework"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:224
#: framework/core/redux-framework/ReduxCore/framework.php:228
msgid "Options"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1733
#: framework/includes/meta-boxes.php:417 framework/includes/meta-boxes.php:691
msgid "Enable"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1738
#: framework/includes/meta-boxes.php:418 framework/includes/meta-boxes.php:692
msgid "Disable"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1744
msgid "moving the mouse over"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1746
msgid "clicking"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1750
#, php-format
msgid ""
"Hints are tooltips that popup when %d the hint icon, offering addition "
"information about the field in which they appear.  They can be %d d by using "
"the link below."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:1755
msgid "Hints"
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:2758
#: framework/core/yolo_reduxframework.php:26
msgid "Invalid security credential.  Please reload the page and try again."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:2767
#: framework/core/yolo_reduxframework.php:35
msgid "Invalid user capability.  Please reload the page and try again."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:2853
#: framework/core/yolo_reduxframework.php:102
msgid "Your panel has no fields. Nothing to save."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:3982
msgid ""
"<strong>Redux Framework Notice: </strong>There are references to the Redux "
"Framework support site in your config's <code>admin_bar_links</code> "
"argument.  This is sample data.  Please change or remove this data before "
"shipping your product."
msgstr ""

#: framework/core/redux-framework/ReduxCore/framework.php:3997
msgid ""
"<strong>Redux Framework Notice: </strong>There are references to the Redux "
"Framework support site in your config's <code>share_icons</code> argument.  "
"This is sample data.  Please change or remove this data before shipping your "
"product."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_admin_notices.php:97
msgid "Dismiss"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_cdn.php:74
msgid ""
"Please wait a few minutes, then try refreshing the page. Unable to load some "
"remotely hosted scripts."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_cdn.php:76
#, php-format
msgid ""
"If you are developing offline, please download and install the <a href=\"%s"
"\" target=\"_blank\">Redux Vendor Support</a> plugin/extension to bypass the "
"our CDN and avoid this warning"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_cdn.php:81
msgid "Redux Framework Warning"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_cdn.php:81
#, php-format
msgid "%s CDN unavailable.  Some controls may not render properly."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_cdn.php:113
#, php-format
msgid ""
"The <a href=\"%s\">Vendor Support plugin</a> (or extension) is either not "
"installed or not activated and thus, some controls may not render properly.  "
"Please ensure that it is installed and <a href=\"%s\">activated</a>"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_filesystem.php:54
#: framework/core/redux-framework/ReduxCore/inc/class.redux_filesystem.php:266
msgid "File Permission Issues"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_filesystem.php:54
#: framework/core/redux-framework/ReduxCore/inc/class.redux_filesystem.php:266
#, php-format
msgid ""
"We were unable to modify required files. Please ensure that <code>%1s</code> "
"has the proper read-write permissions, or modify your wp-config.php file to "
"contain your FTP login credentials as <a href=\"%2s\" target=\"_blank"
"\">outlined here</a>."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/class.redux_helpers.php:610
#, php-format
msgid ""
"<code>%s</code> version <strong style=\"color:red\">%s</strong> is out of "
"date. The core version is %s"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:717
msgid "You have changes that are not saved.  Would you like to save them now?"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:718
msgid "Are you sure?  Resetting will lose all custom values."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/extension_customizer.php:719
msgid ""
"Your current options will be replaced with the values of this preset.  Would "
"you like to proceed?"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_panel.php:89
msgid "Press return or enter to open this panel"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_panel.php:114
#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_panel.php:142
#, php-format
msgid "You are customizing %s"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_panel.php:138
#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_section.php:79
msgid "Back"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_panel.php:146
msgid "Help"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_section.php:72
msgid "Press return or enter to open"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/customizer/inc/customizer_section.php:122
msgid "Press return or enter to expand"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/extension_import_export.php:120
msgid "Import / Export"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:91
msgid "Import Options"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:95
msgid "Import from File"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:98
msgid "Import from URL"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:104
msgid ""
"Input your backup file below and hit Import to restore your sites options "
"from a backup."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:111
msgid ""
"Input the URL to another sites options set and hit Import to load the "
"options from that site."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:116
msgid "Import"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:116
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:120
msgid "Export Options"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:124
msgid ""
"Here you can copy/download your current option settings. Keep this safe as "
"you can use it as a backup should anything go wrong, or you can use it to "
"restore your settings on this site (or any other site)."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:132
msgid "Copy Data"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:133
msgid "Download Data File"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/import_export/import_export/field_import_export.php:134
msgid "Copy Export URL"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/options_object/extension_options_object.php:82
#: framework/core/redux-framework/ReduxCore/inc/extensions/options_object/options_object/field_options_object.php:104
msgid "Options Object"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/extensions/options_object/options_object/field_options_object.php:107
msgid "Show Object in Javascript Console Object"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:141
msgid "Background Repeat"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:157
msgid "Background Clip"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:173
msgid "Background Origin"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:188
msgid "Background Size"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:203
msgid "Background Attachment"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:223
msgid "Background Position"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:277
#: framework/core/redux-framework/ReduxCore/inc/fields/media/field_media.php:149
msgid "No media selected"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/background/field_background.php:314
#: framework/core/redux-framework/ReduxCore/inc/fields/media/field_media.php:198
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:126
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:188
msgid "Upload"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:138
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:215
msgid "Top"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:152
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:229
msgid "Bottom"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/border/field_border.php:174
msgid "Border style"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/color_gradient/field_color_gradient.php:68
msgid "From "
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/color_gradient/field_color_gradient.php:82
msgid "To "
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/dimensions/field_dimensions.php:162
msgid "Width"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/dimensions/field_dimensions.php:178
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:442
msgid "Height"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/dimensions/field_dimensions.php:189
#: framework/core/redux-framework/ReduxCore/inc/fields/dimensions/field_dimensions.php:190
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:245
#: framework/core/redux-framework/ReduxCore/inc/fields/spacing/field_spacing.php:246
msgid "Units"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/gallery/field_gallery.php:73
msgid "Add/Edit Gallery"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/gallery/field_gallery.php:74
msgid "Clear Gallery"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/link_color/field_link_color.php:88
msgid "Regular"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/link_color/field_link_color.php:92
msgid "Hover"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/link_color/field_link_color.php:96
msgid "Visited"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/link_color/field_link_color.php:100
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2404
msgid "Active"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/multi_text/field_multi_text.php:61
msgid "Add More"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/password/field_password.php:62
#: woocommerce/myaccount/form-login.php:46
#: woocommerce/myaccount/form-login.php:97
msgid "Password"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/password/field_password.php:63
#: woocommerce/myaccount/form-login.php:83
msgid "Username"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:68
msgid "Slide"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:73
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:174
#, php-format
msgid "New %s"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:145
#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:200
msgid "Title"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/slides/field_slides.php:223
#, php-format
msgid "Add %s"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/switch/field_switch.php:41
#: framework/includes/options-config.php:1640
#: framework/includes/options-config.php:1955
msgid "On"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/switch/field_switch.php:44
#: framework/includes/options-config.php:1639
#: framework/includes/options-config.php:1954
msgid "Off"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:231
msgid "Font Family"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:232
msgid "Font family"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:258
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:259
msgid "Backup Font Family"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:273
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:281
msgid "Font style"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:274
msgid "Font Weight &amp; Style"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:281
msgid "Style"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:321
msgid "Font subsets"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:323
msgid "Font Subsets"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:325
msgid "Subsets"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:325
msgid "Font script"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:342
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:343
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:344
msgid "Text Align"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:365
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:366
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:367
msgid "Text Transform"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:388
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:389
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:390
msgid "Font Variant"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:408
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:409
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:410
msgid "Text Decoration"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:432
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:433
msgid "Font Size"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:441
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:442
msgid "Line Height"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:450
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:451
msgid "Word Spacing"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:459
#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:460
msgid "Letter Spacing"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:478
msgid "Font Color"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:480
msgid "Font color"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:890
msgid "Standard Fonts"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/fields/typography/field_typography.php:991
msgid "Google Webfonts"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/embedded.php:15
#, php-format
msgid ""
"<span class=\"tc-lead tc-recommended\">RECOMMENDED</span>: If you are "
"submitting to WordPress.org Theme Repository, it is <strong>strongly</"
"strong> suggested that you read <a href=\"%s\" target=\"_blank\">this "
"document</a>, or your theme will be rejected because of Redux."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/embedded.php:24
#, php-format
msgid ""
"<span class=\"tc-lead tc-required\">REQUIRED</span>: You MUST delete "
"<strong> %s </strong>, or your theme will be rejected by WP.org theme "
"submission because of Redux."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/embedded.php:38
msgid "Optional"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/embedded.php:40
msgid ""
"<span class=\"tc-lead tc-recommended\">RECOMMENDED</span>: The following "
"arguments MUST be used for WP.org submissions, or you will be rejected "
"because of your Redux configuration."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:16
msgid "Redux localization utilities"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:17
msgid "Redux Resting Diles"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:18
msgid "Redux Code Styles"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:19
msgid "Redux Unit Testing"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:20
msgid "Redux Plugin File"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:21
msgid "Redux Boostrap Tests"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:22
msgid "CI Testing FIle"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:23
msgid "PHP Unit Testing"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/themecheck/checks/full_package.php:36
msgid ""
"It appears that you have embedded the full Redux package inside your theme. "
"You need only embed the <strong>ReduxCore</strong> folder. Embedding "
"anything else will get your rejected from theme submission. Suspected Redux "
"package file(s):"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:129
msgid "Help improve Our Panel"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:130
msgid ""
"Please helps us improve our panel by allowing us to gather anonymous usage "
"stats so we know which configurations, plugins and themes to test to ensure "
"compatibility."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:135
msgid "Allow tracking"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:140
msgid "Do not allow tracking"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:151
msgid "Welcome to the Redux Demo Panel"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:152
msgid "Getting Started"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:152
#, php-format
msgid ""
"This panel demonstrates the many features of Redux.  Before digging in, we "
"suggest you get up to speed by reviewing %1$s."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:152
msgid "our documentation"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:153
msgid "Redux Generator"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:153
#, php-format
msgid ""
"Want to get a head start? Use the %1$s. It will create a customized "
"boilerplate theme or a standalone admin folder complete with all things "
"Redux (with the help of Underscores and TGM). Save yourself a headache and "
"try it today."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:154
msgid "Redux Extensions"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:154
#, php-format
msgid ""
"Did you know we have extensions, which greatly enhance the features of "
"Redux?  Visit our %1$s to learn more!"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:154
msgid "extensions directory"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:155
msgid "Like Redux?"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:155
#, php-format
msgid ""
"If so, please %1$s and consider making a %2$s to keep development of Redux "
"moving forward."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:155
msgid "leave us a favorable review on WordPress.org"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:155
msgid "donation"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:156
#: framework/includes/yolo-dash/yolo-setup-install.php:143
#: templates/popup-window.php:24
msgid "Newsletter"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:156
msgid ""
"If you'd like to keep up to with all things Redux, please subscribe to our "
"newsletter"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:157
#: framework/includes/options-config.php:338
#: framework/includes/options-config.php:1172
#: framework/includes/options-config.php:3109
#: framework/includes/options-config.php:3774
#: woocommerce/myaccount/form-login.php:90
msgid "Email address"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:157
msgid "Subscribe"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/tracking.php:166
msgid "Close"
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/color/validation_color.php:15
#: framework/core/redux-framework/ReduxCore/inc/validation/color_rgba/validation_color_rgba.php:14
#: framework/core/redux-framework/ReduxCore/inc/validation/colorrgba/validation_colorrgba.php:16
msgid "This field must be a valid color value."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/comma_numeric/validation_comma_numeric.php:16
msgid ""
"You must provide a comma separated list of numerical values for this option."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/css/validation_css.php:39
msgid "Unsafe strings were found in your CSS and have been filtered out."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/date/validation_date.php:16
msgid "This field must be a valid date."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/email/validation_email.php:16
#: framework/core/redux-framework/ReduxCore/inc/validation/email_not_empty/validation_email_not_empty.php:16
msgid "You must provide a valid email for this option."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/no_html/validation_no_html.php:15
msgid ""
"You must not enter any HTML in this field, all HTML tags have been removed."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/no_special_chars/validation_no_special_chars.php:16
msgid ""
"You must not enter any special characters in this field, all special "
"characters have been removed."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/not_empty/validation_not_empty.php:16
msgid "This field cannot be empty. Please provide a value."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/numeric/validation_numeric.php:16
#: framework/core/redux-framework/ReduxCore/inc/validation/numeric_not_empty/validation_numeric_not_empty.php:16
msgid "You must provide a numerical value for this option."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/unique_slug/validation_unique_slug.php:16
#, php-format
msgid ""
"That URL slug is in use, please choose another. <code>%s</code> is open for "
"use."
msgstr ""

#: framework/core/redux-framework/ReduxCore/inc/validation/url/validation_url.php:16
msgid "You must provide a valid URL for this option."
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/footer.tpl.php:57
#: framework/core/redux-framework/ReduxCore/templates/panel/header_stickybar.tpl.php:21
#: framework/core/templates/panel/footer.tpl.php:50
#: framework/core/templates/panel/header.tpl.php:49
msgid "Save Changes"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/footer.tpl.php:61
#: framework/core/redux-framework/ReduxCore/templates/panel/header_stickybar.tpl.php:25
#: framework/core/templates/panel/footer.tpl.php:54
msgid "Reset Section"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/footer.tpl.php:62
#: framework/core/redux-framework/ReduxCore/templates/panel/header_stickybar.tpl.php:26
#: framework/core/templates/panel/footer.tpl.php:55
#: framework/core/templates/panel/header.tpl.php:52
msgid "Reset All"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/footer.tpl.php:67
#: framework/core/redux-framework/ReduxCore/templates/panel/header_stickybar.tpl.php:29
#: framework/core/templates/panel/footer.tpl.php:60
#: framework/core/templates/panel/header.tpl.php:55
msgid "Working..."
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:11
#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:49
#: framework/core/templates/panel/header.tpl.php:11
msgid "Developer Mode Enabled"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:20
#: framework/core/templates/panel/header.tpl.php:20
msgid "WP_DEBUG is enabled"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:26
#: framework/core/templates/panel/header.tpl.php:26
msgid "you are working in a localhost environment"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:31
#: framework/core/templates/panel/header.tpl.php:31
msgid "and"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:34
#: framework/core/templates/panel/header.tpl.php:34
msgid "This has been automatically enabled because"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:36
#: framework/core/templates/panel/header.tpl.php:36
msgid ""
"If you are not a developer, your theme/plugin author shipped with developer "
"mode enabled. Contact them directly to fix it."
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header.tpl.php:58
msgid "FORCED DEV MODE OFF ENABLED"
msgstr ""

#: framework/core/redux-framework/ReduxCore/templates/panel/header_stickybar.tpl.php:15
msgid "Expand"
msgstr ""

#: framework/core/tax-meta-class/tax-meta-class.php:386
msgid "Add"
msgstr ""

#: framework/core/tax-meta-class/tax-meta-class.php:721
msgid "Remove Image"
msgstr ""

#: framework/core/tax-meta-class/tax-meta-class.php:723
msgid "Upload Image"
msgstr ""

#: framework/core/tax-meta-class/tax-meta-class.php:743
msgid "Select a color"
msgstr ""

#: framework/core/templates/panel/footer.tpl.php:48
msgid "Save & Generate CSS"
msgstr ""

#: framework/core/templates/panel/header.tpl.php:43
msgid "motor"
msgstr ""

#: framework/includes/frontend-enqueue.php:141 woocommerce/loop/compare.php:18
#: woocommerce/loop/compare.php:19
#: woocommerce/single-product/product-function.php:17
#: woocommerce/single-product/product-function.php:18
msgid "Compare"
msgstr ""

#: framework/includes/frontend-enqueue.php:142
msgid "WishList"
msgstr ""

#: framework/includes/frontend-enqueue.php:143
msgid "Browse WishList"
msgstr ""

#: framework/includes/frontend-enqueue.php:144
msgid "Quick View"
msgstr ""

#: framework/includes/frontend-enqueue.php:145
msgid "Add To Cart"
msgstr ""

#: framework/includes/meta-boxes.php:43
msgid "Post Format: Image"
msgstr ""

#: framework/includes/meta-boxes.php:52
msgid "Select a image for post"
msgstr ""

#: framework/includes/meta-boxes.php:60
msgid "Post Format: Gallery"
msgstr ""

#: framework/includes/meta-boxes.php:65 templates/archive-heading.php:47
msgid "Images"
msgstr ""

#: framework/includes/meta-boxes.php:68
msgid "Select images gallery for post"
msgstr ""

#: framework/includes/meta-boxes.php:76
msgid "Post Format: Video"
msgstr ""

#: framework/includes/meta-boxes.php:81
msgid "Video URL or Embeded Code"
msgstr ""

#: framework/includes/meta-boxes.php:91
msgid "Post Format: Audio"
msgstr ""

#: framework/includes/meta-boxes.php:96
msgid "Audio URL or Embeded Code"
msgstr ""

#: framework/includes/meta-boxes.php:106
msgid "Post Format: Quote"
msgstr ""

#: framework/includes/meta-boxes.php:111
msgid "Quote"
msgstr ""

#: framework/includes/meta-boxes.php:116
msgid "Author"
msgstr ""

#: framework/includes/meta-boxes.php:121
msgid "Author Url"
msgstr ""

#: framework/includes/meta-boxes.php:130
msgid "Post Format: Link"
msgstr ""

#: framework/includes/meta-boxes.php:135
msgid "Url"
msgstr ""

#: framework/includes/meta-boxes.php:140
msgid "Text"
msgstr ""

#: framework/includes/meta-boxes.php:150 framework/includes/meta-boxes.php:168
msgid "Page Layout"
msgstr ""

#: framework/includes/meta-boxes.php:155
#: framework/includes/options-config.php:145
msgid "Layout Style"
msgstr ""

#: framework/includes/meta-boxes.php:160 templates/switch-selector.php:44
msgid "Boxed"
msgstr ""

#: framework/includes/meta-boxes.php:161 templates/switch-selector.php:42
msgid "Wide"
msgstr ""

#: framework/includes/meta-boxes.php:162
msgid "Float"
msgstr ""

#: framework/includes/meta-boxes.php:173 framework/includes/meta-boxes.php:542
#: framework/includes/options-config.php:409
#: framework/includes/options-config.php:632
#: framework/vc_extension/update_params.php:128
msgid "Full Width"
msgstr ""

#: framework/includes/meta-boxes.php:174 framework/includes/meta-boxes.php:272
#: framework/includes/meta-boxes.php:543
#: framework/includes/options-config.php:410
#: framework/includes/options-config.php:633
#: framework/includes/options-config.php:1337
#: framework/includes/options-config.php:1532
#: framework/includes/options-config.php:1795
#: framework/includes/options-config.php:2054
#: framework/includes/options-config.php:2252
#: framework/includes/options-config.php:2447
#: framework/includes/options-config.php:2965
#: framework/vc_extension/update_params.php:127
msgid "Container"
msgstr ""

#: framework/includes/meta-boxes.php:175 framework/includes/meta-boxes.php:544
#: framework/includes/options-config.php:411
#: framework/includes/options-config.php:634
#: framework/vc_extension/update_params.php:129
msgid "Container Fluid"
msgstr ""

#: framework/includes/meta-boxes.php:182
msgid "Page background color"
msgstr ""

#: framework/includes/meta-boxes.php:184
msgid "Optionally set background color for the page."
msgstr ""

#: framework/includes/meta-boxes.php:189
msgid "Page Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:205
#: framework/includes/options-config.php:451
#: framework/includes/options-config.php:671
#: framework/includes/options-config.php:876
#: framework/includes/options-config.php:966
msgid "Left Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:209 framework/includes/meta-boxes.php:219
#: framework/includes/meta-boxes.php:299 framework/includes/meta-boxes.php:310
#: framework/includes/meta-boxes.php:320
msgid "Select Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:215
#: framework/includes/options-config.php:461
#: framework/includes/options-config.php:682
#: framework/includes/options-config.php:887
#: framework/includes/options-config.php:977
msgid "Right Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:225
#: framework/includes/options-config.php:440
#: framework/includes/options-config.php:657
#: framework/includes/options-config.php:865
#: framework/includes/options-config.php:954
#: framework/includes/options-config.php:4121
msgid "Sidebar Width"
msgstr ""

#: framework/includes/meta-boxes.php:230
#: framework/includes/options-config.php:661
msgid "Small (1/4)"
msgstr ""

#: framework/includes/meta-boxes.php:231
#: framework/includes/options-config.php:662
msgid "Large (1/3)"
msgstr ""

#: framework/includes/meta-boxes.php:239
msgid "Page Class Extra"
msgstr ""

#: framework/includes/meta-boxes.php:250
msgid "Page Top"
msgstr ""

#: framework/includes/meta-boxes.php:255
#: framework/includes/options-config.php:2956
msgid "Show/Hide Top Bar"
msgstr ""

#: framework/includes/meta-boxes.php:261 framework/includes/meta-boxes.php:529
#: framework/includes/meta-boxes.php:716
msgid "Show"
msgstr ""

#: framework/includes/meta-boxes.php:262 framework/includes/meta-boxes.php:530
#: framework/includes/meta-boxes.php:717
msgid "Hide"
msgstr ""

#: framework/includes/meta-boxes.php:267
#: framework/includes/options-config.php:2963
msgid "Top bar layout width"
msgstr ""

#: framework/includes/meta-boxes.php:273
#: framework/includes/options-config.php:1338
#: framework/includes/options-config.php:1533
#: framework/includes/options-config.php:1796
#: framework/includes/options-config.php:2055
#: framework/includes/options-config.php:2253
#: framework/includes/options-config.php:2448
#: framework/includes/options-config.php:2966
msgid "Full width"
msgstr ""

#: framework/includes/meta-boxes.php:278
msgid "Top Bar Layout"
msgstr ""

#: framework/includes/meta-boxes.php:294
#: framework/includes/options-config.php:3014
msgid "Top Left Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:305
#: framework/includes/options-config.php:3024
msgid "Top Right Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:315
#: framework/includes/options-config.php:3034
msgid "Top Center Sidebar"
msgstr ""

#: framework/includes/meta-boxes.php:324
msgid "Top Bar Scheme"
msgstr ""

#: framework/includes/meta-boxes.php:329
msgid "Customize Top Bar Color?"
msgstr ""

#: framework/includes/meta-boxes.php:336
msgid "Top bar text color"
msgstr ""

#: framework/includes/meta-boxes.php:338
msgid "Set top bar text color."
msgstr ""

#: framework/includes/meta-boxes.php:344
msgid "Top bar background color"
msgstr ""

#: framework/includes/meta-boxes.php:346
msgid "Set top bar background color."
msgstr ""

#: framework/includes/meta-boxes.php:352
msgid "Top bar background color opacity"
msgstr ""

#: framework/includes/meta-boxes.php:354
msgid "Set the opacity level of the top bar background color"
msgstr ""

#: framework/includes/meta-boxes.php:373
msgid "Page Header"
msgstr ""

#: framework/includes/meta-boxes.php:378
msgid "Header On/Off?"
msgstr ""

#: framework/includes/meta-boxes.php:381
msgid "Switch header ON or OFF?"
msgstr ""

#: framework/includes/meta-boxes.php:386
msgid "Header Customize On/Off?"
msgstr ""

#: framework/includes/meta-boxes.php:389
msgid "Switch header customize ON or OFF?"
msgstr ""

#: framework/includes/meta-boxes.php:394
#: framework/includes/options-config.php:1182
#: framework/includes/options-config.php:2834
msgid "Header Layout"
msgstr ""

#: framework/includes/meta-boxes.php:411
#: framework/includes/options-config.php:1328
#: framework/includes/options-config.php:1523
#: framework/includes/options-config.php:1786
#: framework/includes/options-config.php:2045
#: framework/includes/options-config.php:2243
#: framework/includes/options-config.php:2438
msgid "Header Float"
msgstr ""

#: framework/includes/meta-boxes.php:420
msgid "Enable/disable header float."
msgstr ""

#: framework/includes/meta-boxes.php:428
#: framework/includes/options-config.php:1101
msgid "Logo"
msgstr ""

#: framework/includes/meta-boxes.php:434
msgid "Custom Logo"
msgstr ""

#: framework/includes/meta-boxes.php:435
msgid "Upload custom logo in header."
msgstr ""

#: framework/includes/meta-boxes.php:441
msgid "Customize Logo Position"
msgstr ""

#: framework/includes/meta-boxes.php:449
msgid "Logo padding top"
msgstr ""

#: framework/includes/meta-boxes.php:450
msgid "Logo padding top. Insert number only (empty to set default value)"
msgstr ""

#: framework/includes/meta-boxes.php:458
msgid "Logo padding bottom"
msgstr ""

#: framework/includes/meta-boxes.php:459
msgid "Logo padding bottom. Insert number only (empty to set default value)"
msgstr ""

#: framework/includes/meta-boxes.php:467
#: framework/includes/options-config.php:1130
msgid "Sticky Logo"
msgstr ""

#: framework/includes/meta-boxes.php:468
msgid "Upload sticky logo in header (empty to default)"
msgstr ""

#: framework/includes/meta-boxes.php:478
msgid "Menu"
msgstr ""

#: framework/includes/meta-boxes.php:483
msgid "Page menu"
msgstr ""

#: framework/includes/meta-boxes.php:487 framework/includes/meta-boxes.php:498
msgid "Select Menu"
msgstr ""

#: framework/includes/meta-boxes.php:490
msgid "Optionally you can choose to override the menu that is used on the page"
msgstr ""

#: framework/includes/meta-boxes.php:494
msgid "Page menu mobile"
msgstr ""

#: framework/includes/meta-boxes.php:501
msgid ""
"Optionally you can choose to override the menu mobile that is used on the "
"page"
msgstr ""

#: framework/includes/meta-boxes.php:518
msgid "Page Title"
msgstr ""

#: framework/includes/meta-boxes.php:523
msgid "Show/Hide Page Title?"
msgstr ""

#: framework/includes/meta-boxes.php:537
#: framework/includes/options-config.php:486
msgid "Page Title Layout"
msgstr ""

#: framework/includes/meta-boxes.php:553
msgid "Custom Page Title"
msgstr ""

#: framework/includes/meta-boxes.php:555 framework/includes/meta-boxes.php:565
msgid "Enter a custom page title if you'd like."
msgstr ""

#: framework/includes/meta-boxes.php:563
msgid "Custom Page Subtitle"
msgstr ""

#: framework/includes/meta-boxes.php:572
msgid "Page Title Scheme"
msgstr ""

#: framework/includes/meta-boxes.php:580
msgid "Page Title Text Color"
msgstr ""

#: framework/includes/meta-boxes.php:582
msgid "Optionally set a text color for the page title."
msgstr ""

#: framework/includes/meta-boxes.php:590
msgid "Page Sub Title Text Color"
msgstr ""

#: framework/includes/meta-boxes.php:592
msgid "Optionally set a text color for the page sub title."
msgstr ""

#: framework/includes/meta-boxes.php:601
#: framework/includes/options-config.php:567
#: framework/includes/options-config.php:574
msgid "Page Title Background Color"
msgstr ""

#: framework/includes/meta-boxes.php:603
msgid "Optionally set a background color for the page title."
msgstr ""

#: framework/includes/meta-boxes.php:610
msgid "Custom Background Image?"
msgstr ""

#: framework/includes/meta-boxes.php:621
msgid "Background Image for page title."
msgstr ""

#: framework/includes/meta-boxes.php:630
msgid "Page Title Overlay Color"
msgstr ""

#: framework/includes/meta-boxes.php:631
msgid "Set an overlay color for page title image."
msgstr ""

#: framework/includes/meta-boxes.php:638
msgid "Custom Overlay Opacity?"
msgstr ""

#: framework/includes/meta-boxes.php:648
msgid "Overlay Opacity"
msgstr ""

#: framework/includes/meta-boxes.php:650
msgid ""
"Set the opacity level of the overlay. This will lighten or darken the image "
"depening on the color selected."
msgstr ""

#: framework/includes/meta-boxes.php:663
msgid "Page Title Style"
msgstr ""

#: framework/includes/meta-boxes.php:670
#: framework/includes/options-config.php:501
msgid "Page Title Text Align"
msgstr ""

#: framework/includes/meta-boxes.php:672
#: framework/includes/options-config.php:502
msgid "Set Page Title Text Align"
msgstr ""

#: framework/includes/meta-boxes.php:685
#: framework/includes/options-config.php:512
msgid "Page Title Parallax"
msgstr ""

#: framework/includes/meta-boxes.php:687
#: framework/includes/options-config.php:513
msgid "Enable Page Title Parallax"
msgstr ""

#: framework/includes/meta-boxes.php:700
#: framework/includes/options-config.php:543 framework/includes/tax-meta.php:42
msgid "Page Title Height"
msgstr ""

#: framework/includes/meta-boxes.php:702
msgid "Enter a page title height value (not include unit)."
msgstr ""

#: framework/includes/meta-boxes.php:710
#: framework/includes/options-config.php:605
#: framework/includes/options-config.php:824
#: framework/includes/options-config.php:1084
msgid "Breadcrumbs"
msgstr ""

#: framework/includes/meta-boxes.php:712
msgid "Show/Hide Breadcrumbs"
msgstr ""

#: framework/includes/meta-boxes.php:722
msgid "Remove Margin Top"
msgstr ""

#: framework/includes/meta-boxes.php:728
msgid "Remove Margin Bottom"
msgstr ""

#: framework/includes/meta-boxes.php:740
msgid "Page Footer"
msgstr ""

#: framework/includes/meta-boxes.php:745
msgid "Select Footer"
msgstr ""

#: framework/includes/meta-boxes.php:748
msgid "Select footer to override footer selected in Theme Options"
msgstr ""

#: framework/includes/options-config.php:83
msgid "General Setting"
msgstr ""

#: framework/includes/options-config.php:84
msgid "Welcome to Motor theme options panel! Have fun customize the theme!"
msgstr ""

#: framework/includes/options-config.php:90
msgid "Page Preloader"
msgstr ""

#: framework/includes/options-config.php:91
msgid "Select Page Preloader. Leave empty if you don't want to use."
msgstr ""

#: framework/includes/options-config.php:120
msgid "Preloader background color"
msgstr ""

#: framework/includes/options-config.php:121
msgid "Set Preloader background color."
msgstr ""

#: framework/includes/options-config.php:129
msgid "Preloader spinner color"
msgstr ""

#: framework/includes/options-config.php:130
msgid "Pick a preloader spinner color for the Top Bar"
msgstr ""

#: framework/includes/options-config.php:138
msgid "Back To Top"
msgstr ""

#: framework/includes/options-config.php:139
msgid "Enable/Disable Back to top button"
msgstr ""

#: framework/includes/options-config.php:146
msgid "Select the layout style"
msgstr ""

#: framework/includes/options-config.php:158
msgid "Site Width (%)"
msgstr ""

#: framework/includes/options-config.php:159
msgid "Set the site width of body"
msgstr ""

#: framework/includes/options-config.php:169
msgid "Site Max Width (px)"
msgstr ""

#: framework/includes/options-config.php:170
msgid "Set the site max width of body"
msgstr ""

#: framework/includes/options-config.php:180
msgid "Body Background Mode"
msgstr ""

#: framework/includes/options-config.php:181
msgid "Chose Background Mode"
msgstr ""

#: framework/includes/options-config.php:195
msgid "Body Background"
msgstr ""

#: framework/includes/options-config.php:196
msgid "Body background (Apply for Boxed layout style)."
msgstr ""

#: framework/includes/options-config.php:211
msgid "Background Pattern"
msgstr ""

#: framework/includes/options-config.php:212
msgid "Body background pattern(Apply for Boxed layout style)"
msgstr ""

#: framework/includes/options-config.php:235
msgid "Google API Key"
msgstr ""

#: framework/includes/options-config.php:236
msgid "Set google API Key for Map"
msgstr ""

#: framework/includes/options-config.php:244
msgid "Enhancement"
msgstr ""

#: framework/includes/options-config.php:251
msgid "Coming Soon / Maintenance Mode"
msgstr ""

#: framework/includes/options-config.php:252
msgid "Enable/Disable your site coming soon / maintenance mode."
msgstr ""

#: framework/includes/options-config.php:266
msgid "Custom Maintenance Mode Page"
msgstr ""

#: framework/includes/options-config.php:267
msgid ""
"If you would like to show a custom page instead of the standard Maintenance "
"page, select the page that is your maintenace page, ."
msgstr ""

#: framework/includes/options-config.php:277
msgid "Maintenance title"
msgstr ""

#: framework/includes/options-config.php:278
msgid "Insert coming soon title."
msgstr ""

#: framework/includes/options-config.php:286
msgid "Maintenance Background"
msgstr ""

#: framework/includes/options-config.php:287
msgid "Select maintenance background image."
msgstr ""

#: framework/includes/options-config.php:297
msgid "Online time"
msgstr ""

#: framework/includes/options-config.php:298
msgid "Your page will automatic end maintenance mode after this time."
msgstr ""

#: framework/includes/options-config.php:305
msgid "Timezone"
msgstr ""

#: framework/includes/options-config.php:306
msgid ""
"You can change timezone from here. More details: http://php.net/manual/en/"
"timezones.php"
msgstr ""

#: framework/includes/options-config.php:315
msgid "Maintenance social profiles"
msgstr ""

#: framework/includes/options-config.php:316
msgid "Select social profile for maintenance page."
msgstr ""

#: framework/includes/options-config.php:318
#: framework/includes/options-config.php:1152
#: framework/includes/options-config.php:3089
#: framework/includes/yolo-dash/yolo-setup-install.php:144
msgid "Twitter"
msgstr ""

#: framework/includes/options-config.php:319
#: framework/includes/options-config.php:1153
#: framework/includes/options-config.php:3090
#: framework/includes/yolo-dash/yolo-setup-install.php:146
msgid "Facebook"
msgstr ""

#: framework/includes/options-config.php:320
#: framework/includes/options-config.php:1154
#: framework/includes/options-config.php:3091
#: framework/includes/yolo-dash/yolo-setup-install.php:145
msgid "Dribbble"
msgstr ""

#: framework/includes/options-config.php:321
#: framework/includes/options-config.php:1155
#: framework/includes/options-config.php:3092
msgid "Vimeo"
msgstr ""

#: framework/includes/options-config.php:322
#: framework/includes/options-config.php:1156
#: framework/includes/options-config.php:3093
msgid "Tumblr"
msgstr ""

#: framework/includes/options-config.php:323
#: framework/includes/options-config.php:1157
#: framework/includes/options-config.php:3094
msgid "Skype"
msgstr ""

#: framework/includes/options-config.php:324
#: framework/includes/options-config.php:1158
#: framework/includes/options-config.php:3095
msgid "LinkedIn"
msgstr ""

#: framework/includes/options-config.php:325
#: framework/includes/options-config.php:1159
#: framework/includes/options-config.php:3096
msgid "Google+"
msgstr ""

#: framework/includes/options-config.php:326
#: framework/includes/options-config.php:1160
#: framework/includes/options-config.php:3097
msgid "Flickr"
msgstr ""

#: framework/includes/options-config.php:327
#: framework/includes/options-config.php:1161
#: framework/includes/options-config.php:3098
msgid "YouTube"
msgstr ""

#: framework/includes/options-config.php:328
#: framework/includes/options-config.php:1162
#: framework/includes/options-config.php:3099
msgid "Pinterest"
msgstr ""

#: framework/includes/options-config.php:329
#: framework/includes/options-config.php:1163
#: framework/includes/options-config.php:3100
msgid "Foursquare"
msgstr ""

#: framework/includes/options-config.php:330
#: framework/includes/options-config.php:1164
#: framework/includes/options-config.php:3101
msgid "Instagram"
msgstr ""

#: framework/includes/options-config.php:331
#: framework/includes/options-config.php:1165
#: framework/includes/options-config.php:3102
msgid "GitHub"
msgstr ""

#: framework/includes/options-config.php:332
#: framework/includes/options-config.php:1166
#: framework/includes/options-config.php:3103
msgid "Xing"
msgstr ""

#: framework/includes/options-config.php:333
#: framework/includes/options-config.php:1167
#: framework/includes/options-config.php:3104
msgid "Behance"
msgstr ""

#: framework/includes/options-config.php:334
#: framework/includes/options-config.php:1168
#: framework/includes/options-config.php:3105
msgid "Deviantart"
msgstr ""

#: framework/includes/options-config.php:335
#: framework/includes/options-config.php:1169
#: framework/includes/options-config.php:3106
msgid "SoundCloud"
msgstr ""

#: framework/includes/options-config.php:336
#: framework/includes/options-config.php:1170
#: framework/includes/options-config.php:3107
msgid "Yelp"
msgstr ""

#: framework/includes/options-config.php:337
#: framework/includes/options-config.php:1171
#: framework/includes/options-config.php:3108
msgid "RSS Feed"
msgstr ""

#: framework/includes/options-config.php:348
msgid "404 Setting"
msgstr ""

#: framework/includes/options-config.php:355
msgid "Page Title 404"
msgstr ""

#: framework/includes/options-config.php:356
msgid "The page"
msgstr ""

#: framework/includes/options-config.php:361
msgid "SubPage Title 404"
msgstr ""

#: framework/includes/options-config.php:362
msgid "You are looking for does not exist"
msgstr ""

#: framework/includes/options-config.php:368
msgid "Background 404 page"
msgstr ""

#: framework/includes/options-config.php:369
msgid "Upload your background image here."
msgstr ""

#: framework/includes/options-config.php:378
msgid "404 Heading"
msgstr ""

#: framework/includes/options-config.php:379
msgid "404 error"
msgstr ""

#: framework/includes/options-config.php:384
msgid "Go back label"
msgstr ""

#: framework/includes/options-config.php:385
msgid "Please return to homepage"
msgstr ""

#: framework/includes/options-config.php:390
msgid "Go back link"
msgstr ""

#: framework/includes/options-config.php:398
msgid "Pages Setting"
msgstr ""

#: framework/includes/options-config.php:405
#: framework/includes/options-config.php:628
#: framework/includes/options-config.php:840
#: framework/includes/options-config.php:929
#: framework/vc_extension/update_params.php:124
#: templates/switch-selector.php:38
msgid "Layout"
msgstr ""

#: framework/includes/options-config.php:406
msgid "Select Page Layout"
msgstr ""

#: framework/includes/options-config.php:419
msgid "Page Background Color"
msgstr ""

#: framework/includes/options-config.php:420
msgid "Select page background color."
msgstr ""

#: framework/includes/options-config.php:426
#: framework/includes/options-config.php:642
#: framework/includes/options-config.php:850
#: framework/includes/options-config.php:939
msgid "Sidebar"
msgstr ""

#: framework/includes/options-config.php:427
#: framework/includes/options-config.php:643
#: framework/includes/options-config.php:851
#: framework/includes/options-config.php:940
msgid "Set Sidebar Style"
msgstr ""

#: framework/includes/options-config.php:441
#: framework/includes/options-config.php:658
#: framework/includes/options-config.php:866
#: framework/includes/options-config.php:955
#: framework/includes/options-config.php:4122
#: framework/includes/options-config.php:4328
msgid "Set Sidebar width"
msgstr ""

#: framework/includes/options-config.php:472
msgid "Page Title Setting"
msgstr ""

#: framework/includes/options-config.php:479
msgid "Show Page Title"
msgstr ""

#: framework/includes/options-config.php:480
msgid "Show/Hide Page Title"
msgstr ""

#: framework/includes/options-config.php:487
msgid "Select Page Title Layout"
msgstr ""

#: framework/includes/options-config.php:524
msgid "Page Title Padding Top"
msgstr ""

#: framework/includes/options-config.php:525
msgid "This must be numeric (no px). Leave blank for default value."
msgstr ""

#: framework/includes/options-config.php:526
msgid ""
"If you would like to override the default page title top/bottom margin, "
"please set it here."
msgstr ""

#: framework/includes/options-config.php:544
msgid "You can set a height for the page title here"
msgstr ""

#: framework/includes/options-config.php:556
msgid "Page Title Background"
msgstr ""

#: framework/includes/options-config.php:557
msgid "Upload page title background."
msgstr ""

#: framework/includes/options-config.php:575
msgid "Pick a background color for page title."
msgstr ""

#: framework/includes/options-config.php:581
msgid "Page Title Background Overlay Color"
msgstr ""

#: framework/includes/options-config.php:582
msgid "Pick a background overlay color for page title."
msgstr ""

#: framework/includes/options-config.php:588
msgid "Page Title Background Overlay Opacity"
msgstr ""

#: framework/includes/options-config.php:589
msgid "Set the opacity level of the overlay."
msgstr ""

#: framework/includes/options-config.php:599
msgid "Breadcrumbs and Comments"
msgstr ""

#: framework/includes/options-config.php:606
msgid "Enable/Disable Breadcrumbs In Pages"
msgstr ""

#: framework/includes/options-config.php:612
msgid "Page Comment"
msgstr ""

#: framework/includes/options-config.php:613
msgid "Enable/Disable page comment"
msgstr ""

#: framework/includes/options-config.php:621
msgid "Archive Page"
msgstr ""

#: framework/includes/options-config.php:629
msgid "Select Archive Layout"
msgstr ""

#: framework/includes/options-config.php:672
msgid "Choose the default left sidebar"
msgstr ""

#: framework/includes/options-config.php:683
msgid "Choose the default right sidebar"
msgstr ""

#: framework/includes/options-config.php:693
#: framework/includes/options-config.php:898
msgid "Paging Style"
msgstr ""

#: framework/includes/options-config.php:694
msgid "Select archive paging style"
msgstr ""

#: framework/includes/options-config.php:699
msgid "Infinity Scroll"
msgstr ""

#: framework/includes/options-config.php:707
#: framework/includes/options-config.php:907
msgid "Paging Align"
msgstr ""

#: framework/includes/options-config.php:708
msgid "Select archive paging align"
msgstr ""

#: framework/includes/options-config.php:721
msgid "Archive Display Type"
msgstr ""

#: framework/includes/options-config.php:722
msgid "Select archive display type"
msgstr ""

#: framework/includes/options-config.php:725
msgid "Large Image"
msgstr ""

#: framework/includes/options-config.php:726
msgid "Medium Image"
msgstr ""

#: framework/includes/options-config.php:727
msgid "Grid"
msgstr ""

#: framework/includes/options-config.php:728
#: framework/includes/options-config.php:4068
msgid "Masonry"
msgstr ""

#: framework/includes/options-config.php:736
msgid "Archive Display Columns"
msgstr ""

#: framework/includes/options-config.php:737
msgid "Choose the number of columns to display on archive pages."
msgstr ""

#: framework/includes/options-config.php:751
msgid "Archive Title Setting"
msgstr ""

#: framework/includes/options-config.php:758
#: framework/includes/options-config.php:4165
msgid "Show Archive Title"
msgstr ""

#: framework/includes/options-config.php:759
msgid "Enable/Disable Archive Title"
msgstr ""

#: framework/includes/options-config.php:766
msgid "Archive Title Layout"
msgstr ""

#: framework/includes/options-config.php:767
msgid "Select Archive Title Layout"
msgstr ""

#: framework/includes/options-config.php:777
msgid "Archive Title Text Align"
msgstr ""

#: framework/includes/options-config.php:778
msgid "Set Archive Title Text Align"
msgstr ""

#: framework/includes/options-config.php:788
msgid "Archive Title Parallax"
msgstr ""

#: framework/includes/options-config.php:789
msgid "Enable Archive Title Parallax"
msgstr ""

#: framework/includes/options-config.php:797
msgid "Archive Title Height"
msgstr ""

#: framework/includes/options-config.php:798
msgid "You can set a height for the archive title here"
msgstr ""

#: framework/includes/options-config.php:812
msgid "Archive Title Background"
msgstr ""

#: framework/includes/options-config.php:813
msgid "Upload archive title background."
msgstr ""

#: framework/includes/options-config.php:825
msgid "Enable/Disable Breadcrumbs In Archive"
msgstr ""

#: framework/includes/options-config.php:833
msgid "Search Page"
msgstr ""

#: framework/includes/options-config.php:841
msgid "Select Search Layout"
msgstr ""

#: framework/includes/options-config.php:899
msgid "Select search paging style"
msgstr ""

#: framework/includes/options-config.php:908
msgid "Select search paging align"
msgstr ""

#: framework/includes/options-config.php:922
msgid "Single Blog"
msgstr ""

#: framework/includes/options-config.php:930
msgid "Select Single Blog Layout"
msgstr ""

#: framework/includes/options-config.php:988
msgid "Show Post Navigation"
msgstr ""

#: framework/includes/options-config.php:989
msgid "Enable/Disable Post Navigation"
msgstr ""

#: framework/includes/options-config.php:996
msgid "Show Author Info"
msgstr ""

#: framework/includes/options-config.php:997
msgid "Enable/Disable Author Info"
msgstr ""

#: framework/includes/options-config.php:1004
msgid "Single Blog Title Setting"
msgstr ""

#: framework/includes/options-config.php:1010
msgid "Show Single Blog Title"
msgstr ""

#: framework/includes/options-config.php:1011
msgid "Enable/Disable Single Blog Title"
msgstr ""

#: framework/includes/options-config.php:1018
msgid "Single Blog Title Layout"
msgstr ""

#: framework/includes/options-config.php:1019
msgid "Select Single Blog Title Layout"
msgstr ""

#: framework/includes/options-config.php:1033
msgid "Single Blog Title Text Align"
msgstr ""

#: framework/includes/options-config.php:1034
msgid "Set Single Blog Title Text Align"
msgstr ""

#: framework/includes/options-config.php:1048
msgid "Single Blog Title Parallax"
msgstr ""

#: framework/includes/options-config.php:1049
msgid "Enable Single Blog Title Parallax"
msgstr ""

#: framework/includes/options-config.php:1057
msgid "Single Blog Title Height"
msgstr ""

#: framework/includes/options-config.php:1058
msgid "You can set a height for the single blog title here"
msgstr ""

#: framework/includes/options-config.php:1072
msgid "Single Blog Title Background"
msgstr ""

#: framework/includes/options-config.php:1073
msgid "Upload single blog title background."
msgstr ""

#: framework/includes/options-config.php:1085
msgid "Enable/Disable Breadcrumbs In Single Blog"
msgstr ""

#: framework/includes/options-config.php:1093
msgid "Logo & Favicon"
msgstr ""

#: framework/includes/options-config.php:1102
#: framework/includes/options-config.php:2865
msgid "Upload your logo here."
msgstr ""

#: framework/includes/options-config.php:1115
#: framework/includes/options-config.php:2902
msgid "Logo Top/Bottom Padding"
msgstr ""

#: framework/includes/options-config.php:1116
msgid "This must be numeric (no px). Leave blank for default."
msgstr ""

#: framework/includes/options-config.php:1117
msgid ""
"If you would like to override the default logo top/bottom padding, then you "
"can do so here."
msgstr ""

#: framework/includes/options-config.php:1131
msgid "Upload a sticky version of your logo here"
msgstr ""

#: framework/includes/options-config.php:1142
msgid "Custom favicon"
msgstr ""

#: framework/includes/options-config.php:1143
msgid ""
"Upload a 16px x 16px Png/Gif/ico image that will represent your website "
"favicon"
msgstr ""

#: framework/includes/options-config.php:1175
msgid "Header"
msgstr ""

#: framework/includes/options-config.php:1183
msgid "Select a header layout option from the examples."
msgstr ""

#: framework/includes/options-config.php:1201
#: framework/includes/options-config.php:1355
#: framework/includes/options-config.php:1550
#: framework/includes/options-config.php:1813
#: framework/includes/options-config.php:2072
#: framework/includes/options-config.php:2270
#: framework/includes/options-config.php:2465
#: framework/includes/options-config.php:2627
msgid "Header Navigation"
msgstr ""

#: framework/includes/options-config.php:1207
msgid "Add register/login popup link to menu"
msgstr ""

#: framework/includes/options-config.php:1208
msgid "Select menu to add register/login popup link."
msgstr ""

#: framework/includes/options-config.php:1214
msgid "Mega Menu Animation"
msgstr ""

#: framework/includes/options-config.php:1215
msgid "Select animation for mega menu"
msgstr ""

#: framework/includes/options-config.php:1231
msgid "Sub menu scheme"
msgstr ""

#: framework/includes/options-config.php:1232
msgid "Set sub menu scheme"
msgstr ""

#: framework/includes/options-config.php:1236
msgid "Customize"
msgstr ""

#: framework/includes/options-config.php:1243
msgid "Sub Menu Background Color"
msgstr ""

#: framework/includes/options-config.php:1244
msgid "Set Sub Menu Background Color."
msgstr ""

#: framework/includes/options-config.php:1252
msgid "Sub Menu Text Color"
msgstr ""

#: framework/includes/options-config.php:1253
msgid "Set Sub Menu Text Color."
msgstr ""

#: framework/includes/options-config.php:1260
msgid "Header Sticky"
msgstr ""

#: framework/includes/options-config.php:1266
msgid "Show/Hide Header Sticky"
msgstr ""

#: framework/includes/options-config.php:1267
msgid "Show Hide header Sticky."
msgstr ""

#: framework/includes/options-config.php:1273
msgid "Header Sticky Effect"
msgstr ""

#: framework/includes/options-config.php:1274
msgid "Choose header sticky effect."
msgstr ""

#: framework/includes/options-config.php:1277
msgid "Slide Up Down"
msgstr ""

#: framework/includes/options-config.php:1278
msgid "Bource"
msgstr ""

#: framework/includes/options-config.php:1279
msgid "Flip"
msgstr ""

#: framework/includes/options-config.php:1280
#: framework/vc_extension/update_params.php:42
msgid "Swing"
msgstr ""

#: framework/includes/options-config.php:1288
msgid "Header sticky scheme"
msgstr ""

#: framework/includes/options-config.php:1289
msgid "Choose header sticky scheme"
msgstr ""

#: framework/includes/options-config.php:1292
msgid "Inherit"
msgstr ""

#: framework/includes/options-config.php:1293
msgid "Gray"
msgstr ""

#: framework/includes/options-config.php:1294
msgid "Light"
msgstr ""

#: framework/includes/options-config.php:1295
msgid "Dark"
msgstr ""

#: framework/includes/options-config.php:1304
msgid "Header 1"
msgstr ""

#: framework/includes/options-config.php:1318
#: framework/includes/options-config.php:1513
#: framework/includes/options-config.php:1776
#: framework/includes/options-config.php:2035
#: framework/includes/options-config.php:2233
#: framework/includes/options-config.php:2428
msgid "Header Height (px)"
msgstr ""

#: framework/includes/options-config.php:1319
#: framework/includes/options-config.php:1514
#: framework/includes/options-config.php:1777
#: framework/includes/options-config.php:2036
#: framework/includes/options-config.php:2234
#: framework/includes/options-config.php:2429
msgid "You can set a height for the header. Empty value to default."
msgstr ""

#: framework/includes/options-config.php:1329
#: framework/includes/options-config.php:1524
#: framework/includes/options-config.php:1787
#: framework/includes/options-config.php:2046
#: framework/includes/options-config.php:2244
#: framework/includes/options-config.php:2439
msgid "Enable/Disable Header Float."
msgstr ""

#: framework/includes/options-config.php:1335
#: framework/includes/options-config.php:1530
#: framework/includes/options-config.php:1793
#: framework/includes/options-config.php:2052
#: framework/includes/options-config.php:2250
#: framework/includes/options-config.php:2445
msgid "Header navigation layout"
msgstr ""

#: framework/includes/options-config.php:1345
#: framework/includes/options-config.php:1540
#: framework/includes/options-config.php:1803
#: framework/includes/options-config.php:2062
#: framework/includes/options-config.php:2260
#: framework/includes/options-config.php:2455
msgid "Header navigation padding left/right (px)"
msgstr ""

#: framework/includes/options-config.php:1361
#: framework/includes/options-config.php:1556
#: framework/includes/options-config.php:1819
#: framework/includes/options-config.php:2078
#: framework/includes/options-config.php:2276
#: framework/includes/options-config.php:2471
msgid "Header navigation distance"
msgstr ""

#: framework/includes/options-config.php:1362
#: framework/includes/options-config.php:1557
#: framework/includes/options-config.php:1820
#: framework/includes/options-config.php:2079
#: framework/includes/options-config.php:2277
#: framework/includes/options-config.php:2472
msgid "You can set distance between navigation items. Empty value to default"
msgstr ""

#: framework/includes/options-config.php:1372
#: framework/includes/options-config.php:1567
#: framework/includes/options-config.php:1830
#: framework/includes/options-config.php:2089
#: framework/includes/options-config.php:2287
#: framework/includes/options-config.php:2482
msgid "Header navigation background color"
msgstr ""

#: framework/includes/options-config.php:1373
#: framework/includes/options-config.php:1568
#: framework/includes/options-config.php:1831
#: framework/includes/options-config.php:2090
#: framework/includes/options-config.php:2288
#: framework/includes/options-config.php:2483
#: framework/includes/options-config.php:2634
msgid "Set header navigation background color"
msgstr ""

#: framework/includes/options-config.php:1379
#: framework/includes/options-config.php:1574
#: framework/includes/options-config.php:1837
#: framework/includes/options-config.php:2096
#: framework/includes/options-config.php:2294
#: framework/includes/options-config.php:2489
msgid "Header navigation text color"
msgstr ""

#: framework/includes/options-config.php:1380
#: framework/includes/options-config.php:1575
#: framework/includes/options-config.php:1838
#: framework/includes/options-config.php:2097
#: framework/includes/options-config.php:2295
#: framework/includes/options-config.php:2490
#: framework/includes/options-config.php:2642
msgid "Set header navigation text color"
msgstr ""

#: framework/includes/options-config.php:1388
#: framework/includes/options-config.php:1705
#: framework/includes/options-config.php:1846
#: framework/includes/options-config.php:2105
#: framework/includes/options-config.php:2303
#: framework/includes/options-config.php:2498
#: framework/includes/options-config.php:2649
msgid "Header Customize Navigation"
msgstr ""

#: framework/includes/options-config.php:1400
#: framework/includes/options-config.php:1463
#: framework/includes/options-config.php:1600
#: framework/includes/options-config.php:1668
#: framework/includes/options-config.php:1715
#: framework/includes/options-config.php:1915
#: framework/includes/options-config.php:1984
#: framework/includes/options-config.php:2115
#: framework/includes/options-config.php:2182
#: framework/includes/options-config.php:2317
#: framework/includes/options-config.php:2377
#: framework/includes/options-config.php:2513
#: framework/includes/options-config.php:2573
#: framework/includes/options-config.php:2660
#: framework/includes/options-config.php:2717
msgid "Social Profile"
msgstr ""

#: framework/includes/options-config.php:1401
#: framework/includes/options-config.php:1464
#: framework/includes/options-config.php:1602
#: framework/includes/options-config.php:1669
#: framework/includes/options-config.php:1718
#: framework/includes/options-config.php:1856
#: framework/includes/options-config.php:1917
#: framework/includes/options-config.php:1985
#: framework/includes/options-config.php:2119
#: framework/includes/options-config.php:2183
#: framework/includes/options-config.php:2314
#: framework/includes/options-config.php:2378
#: framework/includes/options-config.php:2511
#: framework/includes/options-config.php:2574
msgid "Canvas Menu"
msgstr ""

#: framework/includes/options-config.php:1402
#: framework/includes/options-config.php:1459
#: framework/includes/options-config.php:1595
#: framework/includes/options-config.php:1664
#: framework/includes/options-config.php:1719
#: framework/includes/options-config.php:1859
#: framework/includes/options-config.php:1909
#: framework/includes/options-config.php:1980
#: framework/includes/options-config.php:2120
#: framework/includes/options-config.php:2178
#: framework/includes/options-config.php:2318
#: framework/includes/options-config.php:2373
#: framework/includes/options-config.php:2512
#: framework/includes/options-config.php:2569
#: framework/includes/options-config.php:2663
#: framework/includes/options-config.php:2713
#: framework/includes/options-config.php:2941
msgid "Shopping Cart"
msgstr ""

#: framework/includes/options-config.php:1403
#: framework/includes/options-config.php:1456
#: framework/includes/options-config.php:1596
#: framework/includes/options-config.php:1660
#: framework/includes/options-config.php:1720
#: framework/includes/options-config.php:1910
#: framework/includes/options-config.php:1976
#: framework/includes/options-config.php:2121
#: framework/includes/options-config.php:2174
#: framework/includes/options-config.php:2319
#: framework/includes/options-config.php:2369
#: framework/includes/options-config.php:2565
#: framework/includes/options-config.php:2664
msgid "Shopping Cart With Price"
msgstr ""

#: framework/includes/options-config.php:1405
#: framework/includes/options-config.php:1461
#: framework/includes/options-config.php:1598
#: framework/includes/options-config.php:1666
#: framework/includes/options-config.php:1722
#: framework/includes/options-config.php:1861
#: framework/includes/options-config.php:1912
#: framework/includes/options-config.php:1982
#: framework/includes/options-config.php:2123
#: framework/includes/options-config.php:2180
#: framework/includes/options-config.php:2313
#: framework/includes/options-config.php:2375
#: framework/includes/options-config.php:2571
#: framework/includes/options-config.php:2666
#: framework/includes/options-config.php:2715
msgid "Search Button"
msgstr ""

#: framework/includes/options-config.php:1406
#: framework/includes/options-config.php:1462
#: framework/includes/options-config.php:1599
#: framework/includes/options-config.php:1667
#: framework/includes/options-config.php:1723
#: framework/includes/options-config.php:1913
#: framework/includes/options-config.php:1983
#: framework/includes/options-config.php:2124
#: framework/includes/options-config.php:2181
#: framework/includes/options-config.php:2376
#: framework/includes/options-config.php:2515
#: framework/includes/options-config.php:2572
#: framework/includes/options-config.php:2667
#: framework/includes/options-config.php:2712
#: framework/includes/options-config.php:2716
msgid "Search Box"
msgstr ""

#: framework/includes/options-config.php:1407
#: framework/includes/options-config.php:1455
#: framework/includes/options-config.php:1601
#: framework/includes/options-config.php:1659
#: framework/includes/options-config.php:1725
#: framework/includes/options-config.php:1916
#: framework/includes/options-config.php:1975
#: framework/includes/options-config.php:2126
#: framework/includes/options-config.php:2173
#: framework/includes/options-config.php:2321
#: framework/includes/options-config.php:2368
#: framework/includes/options-config.php:2516
#: framework/includes/options-config.php:2564
#: framework/includes/options-config.php:2668
#: framework/includes/options-config.php:2718
msgid "Custom Text"
msgstr ""

#: framework/includes/options-config.php:1418
#: framework/includes/options-config.php:1473
#: framework/includes/options-config.php:1612
#: framework/includes/options-config.php:1678
#: framework/includes/options-config.php:1736
#: framework/includes/options-config.php:1870
#: framework/includes/options-config.php:1927
#: framework/includes/options-config.php:1994
#: framework/includes/options-config.php:2137
#: framework/includes/options-config.php:2192
#: framework/includes/options-config.php:2332
#: framework/includes/options-config.php:2387
#: framework/includes/options-config.php:2528
#: framework/includes/options-config.php:2583
msgid "Custom social profiles"
msgstr ""

#: framework/includes/options-config.php:1419
#: framework/includes/options-config.php:1474
#: framework/includes/options-config.php:1613
#: framework/includes/options-config.php:1679
#: framework/includes/options-config.php:1737
#: framework/includes/options-config.php:1871
#: framework/includes/options-config.php:1928
#: framework/includes/options-config.php:1995
#: framework/includes/options-config.php:2138
#: framework/includes/options-config.php:2193
#: framework/includes/options-config.php:2333
#: framework/includes/options-config.php:2388
#: framework/includes/options-config.php:2529
#: framework/includes/options-config.php:2584
msgid "Select social profile for custom text"
msgstr ""

#: framework/includes/options-config.php:1434
#: framework/includes/options-config.php:1489
#: framework/includes/options-config.php:1628
#: framework/includes/options-config.php:1694
#: framework/includes/options-config.php:1752
#: framework/includes/options-config.php:1886
#: framework/includes/options-config.php:1943
#: framework/includes/options-config.php:2010
#: framework/includes/options-config.php:2153
#: framework/includes/options-config.php:2208
#: framework/includes/options-config.php:2348
#: framework/includes/options-config.php:2403
#: framework/includes/options-config.php:2544
#: framework/includes/options-config.php:2599
msgid "Custom Text Content"
msgstr ""

#: framework/includes/options-config.php:1435
#: framework/includes/options-config.php:1490
#: framework/includes/options-config.php:1629
#: framework/includes/options-config.php:1695
#: framework/includes/options-config.php:1753
#: framework/includes/options-config.php:1887
#: framework/includes/options-config.php:1944
#: framework/includes/options-config.php:2011
#: framework/includes/options-config.php:2154
#: framework/includes/options-config.php:2209
#: framework/includes/options-config.php:2349
#: framework/includes/options-config.php:2404
#: framework/includes/options-config.php:2545
#: framework/includes/options-config.php:2600
msgid "Add Content for Custom Text"
msgstr ""

#: framework/includes/options-config.php:1444
#: framework/includes/options-config.php:1649
#: framework/includes/options-config.php:1965
#: framework/includes/options-config.php:2163
#: framework/includes/options-config.php:2358
#: framework/includes/options-config.php:2554
msgid "Header Customize Right"
msgstr ""

#: framework/includes/options-config.php:1454
#: framework/includes/options-config.php:1592
#: framework/includes/options-config.php:1663
#: framework/includes/options-config.php:1724
#: framework/includes/options-config.php:1906
#: framework/includes/options-config.php:1979
#: framework/includes/options-config.php:2125
#: framework/includes/options-config.php:2177
#: framework/includes/options-config.php:2372
#: framework/includes/options-config.php:2568
msgid "Search Box With Shop Category"
msgstr ""

#: framework/includes/options-config.php:1499
msgid "Header 2"
msgstr ""

#: framework/includes/options-config.php:1582
#: framework/includes/options-config.php:1896
msgid "Header Customize Left"
msgstr ""

#: framework/includes/options-config.php:1636
#: framework/includes/options-config.php:1951
msgid "Header customize separate"
msgstr ""

#: framework/includes/options-config.php:1762
msgid "Header 3"
msgstr ""

#: framework/includes/options-config.php:2021
msgid "Header 4"
msgstr ""

#: framework/includes/options-config.php:2219
msgid "Header 5"
msgstr ""

#: framework/includes/options-config.php:2414
msgid "Header 6"
msgstr ""

#: framework/includes/options-config.php:2610
msgid "Header Sidebar"
msgstr ""

#: framework/includes/options-config.php:2617
msgid "Header Sidebar Width (px)"
msgstr ""

#: framework/includes/options-config.php:2618
msgid "You can set a width for this header sidebar"
msgstr ""

#: framework/includes/options-config.php:2633
msgid "Navigation background color"
msgstr ""

#: framework/includes/options-config.php:2641
msgid "Navigation text color"
msgstr ""

#: framework/includes/options-config.php:2669
#: framework/includes/options-config.php:2719
msgid "Login/Register"
msgstr ""

#: framework/includes/options-config.php:2678
msgid "Social profiles"
msgstr ""

#: framework/includes/options-config.php:2679
#: framework/includes/options-config.php:2729
msgid "Select social for social profile."
msgstr ""

#: framework/includes/options-config.php:2689
#: framework/includes/options-config.php:2739
msgid "Text Content"
msgstr ""

#: framework/includes/options-config.php:2690
#: framework/includes/options-config.php:2740
msgid "Add content for custom text."
msgstr ""

#: framework/includes/options-config.php:2700
msgid "Header Customize After Logo"
msgstr ""

#: framework/includes/options-config.php:2728
msgid "ocial profiles"
msgstr ""

#: framework/includes/options-config.php:2750
msgid "Header Icon Action"
msgstr ""

#: framework/includes/options-config.php:2757
msgid "Search Icon"
msgstr ""

#: framework/includes/options-config.php:2763
msgid "Search Box Type"
msgstr ""

#: framework/includes/options-config.php:2764
msgid "Select search box type."
msgstr ""

#: framework/includes/options-config.php:2767
msgid "Standard"
msgstr ""

#: framework/includes/options-config.php:2768
msgid "Ajax Search"
msgstr ""

#: framework/includes/options-config.php:2775
msgid "Post type for Ajax Search"
msgstr ""

#: framework/includes/options-config.php:2776
msgid "Select post type for ajax search"
msgstr ""

#: framework/includes/options-config.php:2795
msgid "Amount Of Search Result"
msgstr ""

#: framework/includes/options-config.php:2796
msgid "This must be numeric (no px) or empty (default: 8)."
msgstr ""

#: framework/includes/options-config.php:2797
msgid "Set mount of Search Result"
msgstr ""

#: framework/includes/options-config.php:2805
msgid "Woo Icon"
msgstr ""

#: framework/includes/options-config.php:2811
msgid "Shopping Cart Button"
msgstr ""

#: framework/includes/options-config.php:2812
msgid "Select header shopping cart button"
msgstr ""

#: framework/includes/options-config.php:2827
msgid "Mobile Header"
msgstr ""

#: framework/includes/options-config.php:2835
msgid "Select header mobile layout"
msgstr ""

#: framework/includes/options-config.php:2850
msgid "Menu Drop Type"
msgstr ""

#: framework/includes/options-config.php:2851
msgid "Set menu drop type for mobile header"
msgstr ""

#: framework/includes/options-config.php:2854
msgid "Dropdown Menu"
msgstr ""

#: framework/includes/options-config.php:2855
msgid "Fly Menu"
msgstr ""

#: framework/includes/options-config.php:2864
msgid "Mobile Logo"
msgstr ""

#: framework/includes/options-config.php:2875
msgid "Mobile Menu Background"
msgstr ""

#: framework/includes/options-config.php:2876
msgid "Set Mobile Menu Background."
msgstr ""

#: framework/includes/options-config.php:2883
msgid "Mobile Menu Text Color"
msgstr ""

#: framework/includes/options-config.php:2884
msgid "Set Mobile Menu Text color."
msgstr ""

#: framework/includes/options-config.php:2891
msgid "Logo Mobile Max Height"
msgstr ""

#: framework/includes/options-config.php:2892
msgid "You can set a max height for the logo mobile here"
msgstr ""

#: framework/includes/options-config.php:2903
msgid ""
"If you would like to override the default logo top/bottom padding, then you "
"can do so here"
msgstr ""

#: framework/includes/options-config.php:2913
#: framework/includes/options-config.php:2949
msgid "Top Bar"
msgstr ""

#: framework/includes/options-config.php:2914
msgid "Enable Top bar."
msgstr ""

#: framework/includes/options-config.php:2920
msgid "Stick Mobile Header"
msgstr ""

#: framework/includes/options-config.php:2921
msgid "Enable Stick Mobile Header."
msgstr ""

#: framework/includes/options-config.php:2927
msgid "Header Search Icon"
msgstr ""

#: framework/includes/options-config.php:2928
msgid "Enable Header Search Icon."
msgstr ""

#: framework/includes/options-config.php:2934
msgid "Search Menu Box"
msgstr ""

#: framework/includes/options-config.php:2935
msgid "Enable Search Menu Box."
msgstr ""

#: framework/includes/options-config.php:2942
msgid "Enable Shopping Cart"
msgstr ""

#: framework/includes/options-config.php:2957
msgid "Show Hide Top Bar."
msgstr ""

#: framework/includes/options-config.php:2974
msgid "Top bar padding left/right (px)"
msgstr ""

#: framework/includes/options-config.php:2984
msgid "Top Bar background color"
msgstr ""

#: framework/includes/options-config.php:2985
msgid "Set Top Bar background color."
msgstr ""

#: framework/includes/options-config.php:2992
msgid "Top Bar text color"
msgstr ""

#: framework/includes/options-config.php:2993
msgid "Pick a text color for the Top Bar"
msgstr ""

#: framework/includes/options-config.php:2999
msgid "Top bar Layout"
msgstr ""

#: framework/includes/options-config.php:3000
msgid "Select the top bar column layout."
msgstr ""

#: framework/includes/options-config.php:3046
msgid "Portfolio"
msgstr ""

#: framework/includes/options-config.php:3054
msgid "Disable link to detail"
msgstr ""

#: framework/includes/options-config.php:3055
msgid "Enable/Disable link to detail in Portfolio"
msgstr ""

#: framework/includes/options-config.php:3062
msgid "Portfolio Single Settings"
msgstr ""

#: framework/includes/options-config.php:3069
msgid "Single Portfolio Layout"
msgstr ""

#: framework/includes/options-config.php:3070
msgid "Select Single Portfolio Layout"
msgstr ""

#: framework/includes/options-config.php:3086
msgid "Portfolio social profiles"
msgstr ""

#: framework/includes/options-config.php:3087
msgid "Select social profile for portfolio single."
msgstr ""

#: framework/includes/options-config.php:3118
msgid "Portfolio Related"
msgstr ""

#: framework/includes/options-config.php:3125
msgid "Show/Hide Related"
msgstr ""

#: framework/includes/options-config.php:3126
msgid "Show or hide related in single portfolio"
msgstr ""

#: framework/includes/options-config.php:3136
msgid "Select portfolio related style"
msgstr ""

#: framework/includes/options-config.php:3139
msgid "Squared"
msgstr ""

#: framework/includes/options-config.php:3140
msgid "Landscape"
msgstr ""

#: framework/includes/options-config.php:3141
msgid "Portrait"
msgstr ""

#: framework/includes/options-config.php:3150
msgid "Portfolio Related Column"
msgstr ""

#: framework/includes/options-config.php:3151
msgid "Select Portfolio Related Column."
msgstr ""

#: framework/includes/options-config.php:3164
msgid "Portfolio Related Orverlay Style"
msgstr ""

#: framework/includes/options-config.php:3165
msgid ""
"Select Portfolio Related Orverlay Style.  Only apply for Portfolio related "
"style is grid"
msgstr ""

#: framework/includes/options-config.php:3181
msgid "Select portfolio related hover effect"
msgstr ""

#: framework/includes/options-config.php:3183
msgid "Effect 1"
msgstr ""

#: framework/includes/options-config.php:3184
msgid "Effect 2"
msgstr ""

#: framework/includes/options-config.php:3185
msgid "Effect 3"
msgstr ""

#: framework/includes/options-config.php:3186
msgid "Effect 4"
msgstr ""

#: framework/includes/options-config.php:3187
msgid "Effect 5"
msgstr ""

#: framework/includes/options-config.php:3196
msgid "Single Portfolio Title"
msgstr ""

#: framework/includes/options-config.php:3202
msgid "Inherit Page Title "
msgstr ""

#: framework/includes/options-config.php:3203
msgid "Enable/Disable Inherit Page Title Setting"
msgstr ""

#: framework/includes/options-config.php:3209
msgid "Show Portfolio Title"
msgstr ""

#: framework/includes/options-config.php:3210
msgid "Enable/Disable Portfolio Title"
msgstr ""

#: framework/includes/options-config.php:3217
msgid "Portfolio Title Parallax"
msgstr ""

#: framework/includes/options-config.php:3218
msgid "Enable Portfolio Title Parallax"
msgstr ""

#: framework/includes/options-config.php:3228
msgid "Portfolio Title Height"
msgstr ""

#: framework/includes/options-config.php:3229
#: framework/includes/options-config.php:4423
msgid "This must be numeric (no px) or empty."
msgstr ""

#: framework/includes/options-config.php:3230
msgid "You can set a height for the Portfolio title here"
msgstr ""

#: framework/includes/options-config.php:3243
msgid "Portfolio Title Background"
msgstr ""

#: framework/includes/options-config.php:3244
msgid "Upload portfolio title background."
msgstr ""

#: framework/includes/options-config.php:3255
msgid "Breadcrumbs in Portfolio"
msgstr ""

#: framework/includes/options-config.php:3256
msgid "Enable/Disable Breadcrumbs in Portfolio"
msgstr ""

#: framework/includes/options-config.php:3266
msgid "Footer"
msgstr ""

#: framework/includes/options-config.php:3273
msgid "Select Footer Block"
msgstr ""

#: framework/includes/options-config.php:3274
msgid "Set Footer Block"
msgstr ""

#: framework/includes/options-config.php:3282
msgid "Styling Options"
msgstr ""

#: framework/includes/options-config.php:3283
#: framework/includes/options-config.php:3325
msgid "If you change value in this section, you must \"Save & Generate CSS\""
msgstr ""

#: framework/includes/options-config.php:3289 templates/switch-selector.php:24
msgid "Primary Color"
msgstr ""

#: framework/includes/options-config.php:3290
msgid "Set Primary Color"
msgstr ""

#: framework/includes/options-config.php:3297
msgid "Secondary Color"
msgstr ""

#: framework/includes/options-config.php:3298
msgid "Set Secondary Color"
msgstr ""

#: framework/includes/options-config.php:3306
msgid "Text Color"
msgstr ""

#: framework/includes/options-config.php:3307
msgid "Set Text Color."
msgstr ""

#: framework/includes/options-config.php:3314
msgid "Heading Color"
msgstr ""

#: framework/includes/options-config.php:3315
msgid "Set Heading Color."
msgstr ""

#: framework/includes/options-config.php:3324
msgid "Typography"
msgstr ""

#: framework/includes/options-config.php:3331
msgid "Body Font"
msgstr ""

#: framework/includes/options-config.php:3332
msgid "Specify the body font properties."
msgstr ""

#: framework/includes/options-config.php:3354
msgid "Secondary Font"
msgstr ""

#: framework/includes/options-config.php:3355
msgid "Specify the Secondary font properties."
msgstr ""

#: framework/includes/options-config.php:3379
msgid "Heading Font"
msgstr ""

#: framework/includes/options-config.php:3386
msgid "H1 Font"
msgstr ""

#: framework/includes/options-config.php:3387
msgid "Specify the H1 font properties."
msgstr ""

#: framework/includes/options-config.php:3407
msgid "H2 Font"
msgstr ""

#: framework/includes/options-config.php:3408
msgid "Specify the H2 font properties."
msgstr ""

#: framework/includes/options-config.php:3428
msgid "H3 Font"
msgstr ""

#: framework/includes/options-config.php:3429
msgid "Specify the H3 font properties."
msgstr ""

#: framework/includes/options-config.php:3449
msgid "H4 Font"
msgstr ""

#: framework/includes/options-config.php:3450
msgid "Specify the H4 font properties."
msgstr ""

#: framework/includes/options-config.php:3470
msgid "H5 Font"
msgstr ""

#: framework/includes/options-config.php:3471
msgid "Specify the H5 font properties."
msgstr ""

#: framework/includes/options-config.php:3491
msgid "H6 Font"
msgstr ""

#: framework/includes/options-config.php:3492
msgid "Specify the H6 font properties."
msgstr ""

#: framework/includes/options-config.php:3513
#: framework/includes/options-config.php:3521
msgid "Menu Font"
msgstr ""

#: framework/includes/options-config.php:3522
msgid "Specify the Menu font properties."
msgstr ""

#: framework/includes/options-config.php:3545
#: framework/includes/options-config.php:3552
msgid "Page Title Font"
msgstr ""

#: framework/includes/options-config.php:3553
msgid "Specify the page title font properties."
msgstr ""

#: framework/includes/options-config.php:3579
msgid "Page Sub Title Font"
msgstr ""

#: framework/includes/options-config.php:3580
msgid "Specify the page sub title font properties."
msgstr ""

#: framework/includes/options-config.php:3607
msgid "Social Profiles"
msgstr ""

#: framework/includes/options-config.php:3614
msgid "Facebook URL"
msgstr ""

#: framework/includes/options-config.php:3622
msgid "Twitter URL"
msgstr ""

#: framework/includes/options-config.php:3631
msgid "Dribbble URL"
msgstr ""

#: framework/includes/options-config.php:3639
msgid "Vimeo URL"
msgstr ""

#: framework/includes/options-config.php:3647
msgid "Tumblr URL"
msgstr ""

#: framework/includes/options-config.php:3655
msgid "Skype ID"
msgstr ""

#: framework/includes/options-config.php:3662
msgid "LinkedIn URL"
msgstr ""

#: framework/includes/options-config.php:3670
msgid "Google+ URL"
msgstr ""

#: framework/includes/options-config.php:3678
msgid "Flickr URL"
msgstr ""

#: framework/includes/options-config.php:3686
msgid "YouTube URL"
msgstr ""

#: framework/includes/options-config.php:3694
msgid "Pinterest URL"
msgstr ""

#: framework/includes/options-config.php:3702
msgid "Foursquare URL"
msgstr ""

#: framework/includes/options-config.php:3710
msgid "Instagram URL"
msgstr ""

#: framework/includes/options-config.php:3718
msgid "GitHub URL"
msgstr ""

#: framework/includes/options-config.php:3726
msgid "Xing URL"
msgstr ""

#: framework/includes/options-config.php:3734
msgid "Behance URL"
msgstr ""

#: framework/includes/options-config.php:3742
msgid "Deviantart URL"
msgstr ""

#: framework/includes/options-config.php:3750
msgid "SoundCloud URL"
msgstr ""

#: framework/includes/options-config.php:3758
msgid "Yelp URL"
msgstr ""

#: framework/includes/options-config.php:3766
msgid "RSS Feed URL"
msgstr ""

#: framework/includes/options-config.php:3784
msgid "Social Share"
msgstr ""

#: framework/includes/options-config.php:3787
msgid "Show the social sharing in blog posts"
msgstr ""

#: framework/includes/options-config.php:3814
msgid "Promo Popup"
msgstr ""

#: framework/includes/options-config.php:3821
msgid "Show Popup"
msgstr ""

#: framework/includes/options-config.php:3822
msgid "Show/Hide Popup when user go to your site"
msgstr ""

#: framework/includes/options-config.php:3828
msgid "Popup Width"
msgstr ""

#: framework/includes/options-config.php:3838
msgid "Popup Height"
msgstr ""

#: framework/includes/options-config.php:3848
msgid "Popup Effect"
msgstr ""

#: framework/includes/options-config.php:3849
msgid "Choose popup effect."
msgstr ""

#: framework/includes/options-config.php:3851
msgid "ZoomIn"
msgstr ""

#: framework/includes/options-config.php:3852
msgid "Newspaper"
msgstr ""

#: framework/includes/options-config.php:3853
msgid "Move Horizontal"
msgstr ""

#: framework/includes/options-config.php:3854
msgid "Move From Top"
msgstr ""

#: framework/includes/options-config.php:3855
msgid "3D Unfold"
msgstr ""

#: framework/includes/options-config.php:3856
msgid "ZoomOut"
msgstr ""

#: framework/includes/options-config.php:3857
msgid "Hinge"
msgstr ""

#: framework/includes/options-config.php:3866
msgid "Popup Delay"
msgstr ""

#: framework/includes/options-config.php:3876
msgid "Popup Content"
msgstr ""

#: framework/includes/options-config.php:3885
msgid "Popup Background"
msgstr ""

#: framework/includes/options-config.php:3900 framework/includes/sidebar.php:61
#: framework/includes/sidebar.php:63
msgid "Woocommerce"
msgstr ""

#: framework/includes/options-config.php:3907
msgid "Show Rating"
msgstr ""

#: framework/includes/options-config.php:3908
msgid "Show/Hide Rating product"
msgstr ""

#: framework/includes/options-config.php:3916
msgid "Sale Badge Mode"
msgstr ""

#: framework/includes/options-config.php:3917
msgid "Chose Sale Badge Mode"
msgstr ""

#: framework/includes/options-config.php:3929
msgid "Show Result Count"
msgstr ""

#: framework/includes/options-config.php:3930
msgid "Show/Hide Result Count In Archive Product"
msgstr ""

#: framework/includes/options-config.php:3937
msgid "Show Catalog Ordering"
msgstr ""

#: framework/includes/options-config.php:3938
msgid "Show/Hide Catalog Ordering"
msgstr ""

#: framework/includes/options-config.php:3945
msgid "Button Tooltip"
msgstr ""

#: framework/includes/options-config.php:3946
msgid "Enable/Disable Button Tooltip"
msgstr ""

#: framework/includes/options-config.php:3952
msgid "Quick View Button"
msgstr ""

#: framework/includes/options-config.php:3953
msgid "Enable/Disable Quick View"
msgstr ""

#: framework/includes/options-config.php:3959
msgid "Add To Cart Button"
msgstr ""

#: framework/includes/options-config.php:3960
msgid "Enable/Disable Add To Cart Button"
msgstr ""

#: framework/includes/options-config.php:3966
msgid "Add To Wishlist Button"
msgstr ""

#: framework/includes/options-config.php:3967
msgid "Enable/Disable Add To Wishlist Button"
msgstr ""

#: framework/includes/options-config.php:3973
msgid "Add To Compare Button"
msgstr ""

#: framework/includes/options-config.php:3974
msgid "Enable/Disable Add To Compare Button"
msgstr ""

#: framework/includes/options-config.php:3982
msgid "Archive Product"
msgstr ""

#: framework/includes/options-config.php:3989
msgid "Show Page Shop Content"
msgstr ""

#: framework/includes/options-config.php:3990
msgid "Enable/Disable Shop Page Content"
msgstr ""

#: framework/includes/options-config.php:3998
msgid "Products Per Page"
msgstr ""

#: framework/includes/options-config.php:3999
msgid "This must be numeric or empty (default 12)."
msgstr ""

#: framework/includes/options-config.php:4000
msgid "Set Products Per Page in archive product"
msgstr ""

#: framework/includes/options-config.php:4007
msgid "Product Display Columns"
msgstr ""

#: framework/includes/options-config.php:4008
msgid "Choose the number of columns to display on shop/category pages."
msgstr ""

#: framework/includes/options-config.php:4023
msgid "Product Mobile Display Columns"
msgstr ""

#: framework/includes/options-config.php:4024
msgid ""
"Choose the number of columns to display on shop/category pages on the mobile "
"( under 480px )."
msgstr ""

#: framework/includes/options-config.php:4038
#: framework/includes/options-config.php:4294
msgid "Layout Options"
msgstr ""

#: framework/includes/options-config.php:4044
msgid "Archive Product Layout"
msgstr ""

#: framework/includes/options-config.php:4045
msgid "Select Archive Product Layout"
msgstr ""

#: framework/includes/options-config.php:4053
msgid "Shop page style"
msgstr ""

#: framework/includes/options-config.php:4054
msgid "Select shop page style"
msgstr ""

#: framework/includes/options-config.php:4064
msgid "Select Product Style"
msgstr ""

#: framework/includes/options-config.php:4067
msgid "FitRows"
msgstr ""

#: framework/includes/options-config.php:4075
msgid "Show Categories"
msgstr ""

#: framework/includes/options-config.php:4076
msgid "Show/Hide categories"
msgstr ""

#: framework/includes/options-config.php:4083
msgid "Show Filters"
msgstr ""

#: framework/includes/options-config.php:4084
msgid "Show/Hide filters"
msgstr ""

#: framework/includes/options-config.php:4091
msgid "Show Search"
msgstr ""

#: framework/includes/options-config.php:4092
msgid "Show/Hide search"
msgstr ""

#: framework/includes/options-config.php:4099
msgid "Ajax filter"
msgstr ""

#: framework/includes/options-config.php:4100
msgid ""
"Use Ajax to filter shop content (Ajax allows new content without reloading "
"the whole page)."
msgstr ""

#: framework/includes/options-config.php:4107
msgid "Archive Product Sidebar"
msgstr ""

#: framework/includes/options-config.php:4108
msgid "Set Archive Product Sidebar"
msgstr ""

#: framework/includes/options-config.php:4134
msgid "Archive Product Left Sidebar"
msgstr ""

#: framework/includes/options-config.php:4144
msgid "Archive Product Right Sidebar"
msgstr ""

#: framework/includes/options-config.php:4159
#: framework/includes/options-config.php:4366
msgid "Page Title Options"
msgstr ""

#: framework/includes/options-config.php:4166
msgid "Enable/Disable Archive Product Title"
msgstr ""

#: framework/includes/options-config.php:4173
msgid "Archive Product Title Layout"
msgstr ""

#: framework/includes/options-config.php:4174
msgid "Select Archive Product Title Layout"
msgstr ""

#: framework/includes/options-config.php:4208
msgid "Archive Product Title Text Align"
msgstr ""

#: framework/includes/options-config.php:4209
msgid "Set Archive Product Title Text Align"
msgstr ""

#: framework/includes/options-config.php:4223
msgid "Archive Product Title Parallax"
msgstr ""

#: framework/includes/options-config.php:4224
msgid "Enable Archive Product Title Parallax"
msgstr ""

#: framework/includes/options-config.php:4233
msgid "Archive Product Title Height"
msgstr ""

#: framework/includes/options-config.php:4234
msgid "You can set a height for the archive product title here"
msgstr ""

#: framework/includes/options-config.php:4248
msgid "Archive Product Title Background"
msgstr ""

#: framework/includes/options-config.php:4249
msgid "Upload archive product title background."
msgstr ""

#: framework/includes/options-config.php:4260
msgid "Breadcrumbs in Archive Product"
msgstr ""

#: framework/includes/options-config.php:4261
msgid "Enable/Disable Breadcrumbs in Archive Product"
msgstr ""

#: framework/includes/options-config.php:4269
msgid "Single Product"
msgstr ""

#: framework/includes/options-config.php:4277
msgid "Enable Zoom Image"
msgstr ""

#: framework/includes/options-config.php:4278
msgid "Enable/Disable Zoom Image for Product Image"
msgstr ""

#: framework/includes/options-config.php:4285
msgid "Show Image Thumb"
msgstr ""

#: framework/includes/options-config.php:4286
msgid "Show/Hide Image Thumb product"
msgstr ""

#: framework/includes/options-config.php:4301
msgid "Single Product Layout"
msgstr ""

#: framework/includes/options-config.php:4302
msgid "Select Single Product Layout"
msgstr ""

#: framework/includes/options-config.php:4314
msgid "Single Product Sidebar"
msgstr ""

#: framework/includes/options-config.php:4315
msgid "Set Single Product Sidebar"
msgstr ""

#: framework/includes/options-config.php:4327
msgid "Single Product Sidebar Width"
msgstr ""

#: framework/includes/options-config.php:4337
msgid "Single Product Left Sidebar"
msgstr ""

#: framework/includes/options-config.php:4347
msgid "Single Product Right Sidebar"
msgstr ""

#: framework/includes/options-config.php:4373
msgid "Show Single Title"
msgstr ""

#: framework/includes/options-config.php:4374
msgid "Enable/Disable Single Product Title"
msgstr ""

#: framework/includes/options-config.php:4381
msgid "Custom Single Product Title"
msgstr ""

#: framework/includes/options-config.php:4382
msgid "Enter a custom page title if you would like. "
msgstr ""

#: framework/includes/options-config.php:4384
msgid "Product Details"
msgstr ""

#: framework/includes/options-config.php:4391
msgid "Single Product Title Layout"
msgstr ""

#: framework/includes/options-config.php:4392
msgid "Select Single Product Title Layout"
msgstr ""

#: framework/includes/options-config.php:4402
msgid "Single Product Title Text Align"
msgstr ""

#: framework/includes/options-config.php:4403
msgid "Set Single Product Title Text Align"
msgstr ""

#: framework/includes/options-config.php:4413
msgid "Single Product Title Parallax"
msgstr ""

#: framework/includes/options-config.php:4414
msgid "Enable Single Product Title Parallax"
msgstr ""

#: framework/includes/options-config.php:4422
msgid "Single Product Title Height"
msgstr ""

#: framework/includes/options-config.php:4424
msgid "You can set a height for the single product title here"
msgstr ""

#: framework/includes/options-config.php:4438
msgid "Single Product Title Background"
msgstr ""

#: framework/includes/options-config.php:4439
msgid "Upload single product title background."
msgstr ""

#: framework/includes/options-config.php:4450
msgid "Breadcrumbs in Single Product"
msgstr ""

#: framework/includes/options-config.php:4451
msgid "Enable/Disable Breadcrumbs in Single Product"
msgstr ""

#: framework/includes/options-config.php:4465
msgid "Product Related Options"
msgstr ""

#: framework/includes/options-config.php:4471
msgid "Related Product Total Record"
msgstr ""

#: framework/includes/options-config.php:4472
msgid "Total Record Of Related Product."
msgstr ""

#: framework/includes/options-config.php:4480
msgid "Related Product Display Columns"
msgstr ""

#: framework/includes/options-config.php:4481
msgid "Choose the number of columns to display on related product."
msgstr ""

#: framework/includes/options-config.php:4493
msgid "Related Product Condition"
msgstr ""

#: framework/includes/options-config.php:4495
msgid "Same Category"
msgstr ""

#: framework/includes/options-config.php:4496
msgid "Same Tag"
msgstr ""

#: framework/includes/options-config.php:4515
msgid "Custom CSS & Script"
msgstr ""

#: framework/includes/options-config.php:4516
msgid "If you change Custom CSS, you must \"Save & Generate CSS\""
msgstr ""

#: framework/includes/options-config.php:4524
msgid "Custom CSS"
msgstr ""

#: framework/includes/options-config.php:4525
msgid ""
"Add some CSS to your theme by adding it to this textarea. Please do not "
"include any style tags."
msgstr ""

#: framework/includes/options-config.php:4535
msgid "Custom JS"
msgstr ""

#: framework/includes/options-config.php:4536
msgid ""
"Add some custom JavaScript to your theme by adding it to this textarea. "
"Please do not include any script tags."
msgstr ""

#: framework/includes/options-config.php:4569
#: framework/includes/options-config.php:4570
msgid "Theme Options"
msgstr ""

#: framework/includes/sidebar.php:17
msgid "Sidebar 1"
msgstr ""

#: framework/includes/sidebar.php:19
msgid "Widget Area 1"
msgstr ""

#: framework/includes/sidebar.php:28
msgid "Sidebar 2"
msgstr ""

#: framework/includes/sidebar.php:30
msgid "Widget Area 2"
msgstr ""

#: framework/includes/sidebar.php:39 framework/includes/sidebar.php:41
msgid "Top Bar Left"
msgstr ""

#: framework/includes/sidebar.php:50 framework/includes/sidebar.php:52
msgid "Top Bar Right"
msgstr ""

#: framework/includes/sidebar.php:72
msgid "Woocommerce Shop Filter"
msgstr ""

#: framework/includes/sidebar.php:74
msgid "Woocommerce Shop filter"
msgstr ""

#: framework/includes/sidebar.php:83
msgid "Off Canvas Menu"
msgstr ""

#: framework/includes/sidebar.php:85
msgid "Canvas Menu Widget Area"
msgstr ""

#: framework/includes/tax-meta.php:26
msgid "Category Meta Box"
msgstr ""

#: framework/includes/tax-meta.php:45
msgid "Page Title Background "
msgstr ""

#: framework/includes/theme-filter.php:24
msgid "Name*"
msgstr ""

#: framework/includes/theme-filter.php:27
msgid "Email*"
msgstr ""

#: framework/includes/theme-filter.php:47
msgid "Message*"
msgstr ""

#: framework/includes/theme-filter.php:51
msgid "Send us now"
msgstr ""

#: framework/includes/theme-filter.php:102
msgid "Search..."
msgstr ""

#: framework/includes/theme-functions.php:17
msgid "Custom Sidebar Name"
msgstr ""

#: framework/includes/theme-functions.php:18
msgid "Add Sidebar"
msgstr ""

#: framework/includes/theme-setup.php:47
msgid "Primary Menu"
msgstr ""

#: framework/includes/theme-setup.php:48
msgid "Mobile Menu"
msgstr ""

#: framework/includes/widget-custom-class.php:5
msgid "CSS Class"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:195
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:343
#, php-format
msgid "This theme requires the following plugin: %1$s."
msgid_plural "This theme requires the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:196
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:349
#, php-format
msgid "This theme recommends the following plugin: %1$s."
msgid_plural "This theme recommends the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:197
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to install the %s plugin. "
"Contact the administrator of this site for help on getting the plugin "
"installed."
msgid_plural ""
"Sorry, but you do not have the correct permissions to install the %s "
"plugins. Contact the administrator of this site for help on getting the "
"plugins installed."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:198
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:367
#, php-format
msgid "The following required plugin is currently inactive: %1$s."
msgid_plural "The following required plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:199
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:373
#, php-format
msgid "The following recommended plugin is currently inactive: %1$s."
msgid_plural "The following recommended plugins are currently inactive: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:200
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to activate the %s "
"plugin. Contact the administrator of this site for help on getting the "
"plugin activated."
msgid_plural ""
"Sorry, but you do not have the correct permissions to activate the %s "
"plugins. Contact the administrator of this site for help on getting the "
"plugins activated."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:201
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:355
#, php-format
msgid ""
"The following plugin needs to be updated to its latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgid_plural ""
"The following plugins need to be updated to their latest version to ensure "
"maximum compatibility with this theme: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:202
#, php-format
msgid ""
"Sorry, but you do not have the correct permissions to update the %s plugin. "
"Contact the administrator of this site for help on getting the plugin "
"updated."
msgid_plural ""
"Sorry, but you do not have the correct permissions to update the %s plugins. "
"Contact the administrator of this site for help on getting the plugins "
"updated."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:203
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:378
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:204
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:388
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/register-require-plugin.php:246
msgid "WPBakery Visual Composer"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:266
msgid "Motor Framework"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:285
msgid "Revolution Slider"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:305
msgid "Instagram Feed"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:337
#: framework/includes/yolo-dash/register-require-plugin.php:370
#, php-format
msgid ""
"Purchase code verification failed. <a href=\"%s\">Enter Purchase Code</a>"
msgstr ""

#: framework/includes/yolo-dash/register-require-plugin.php:361
#: framework/includes/yolo-dash/yolo-setup-install.php:285
msgid "Some troubles with connecting to YoloTheme server."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:337
#, php-format
msgid "Installing Plugin: %s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:339
#, php-format
msgid "Updating Plugin: %s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:361
#, php-format
msgid "There is an update available for: %1$s."
msgid_plural "There are updates available for the following plugins: %1$s."
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:383
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:393
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:910
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2616
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3663
msgid "Return to the Dashboard"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:394
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3242
msgid "Plugin activated successfully."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:395
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3035
msgid "The following plugin was activated successfully:"
msgid_plural "The following plugins were activated successfully:"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:397
#, php-format
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:399
#, php-format
msgid ""
"Plugin not activated. A higher version of %s is needed for this theme. "
"Please update the plugin."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:401
#, php-format
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:403
msgid ""
"There are one or more required or recommended plugins to install, update or "
"activate."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:1017
msgid ""
"The remote plugin package does not contain a folder with the desired slug "
"and renaming did not work."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:1017
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:1020
msgid ""
"Please contact the plugin provider and ask them to package their plugin "
"according to the WordPress guidelines."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:1020
msgid ""
"The remote plugin package consists of more than one file, but the files are "
"not packaged in a folder."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:1204
#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3031
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2065
#, php-format
msgid "TGMPA v%s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2356
msgid "Required"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2359
msgid "Recommended"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2375
msgid "WordPress Repository"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2378
msgid "External Source"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2381
msgid "Pre-Packaged"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2398
msgid "Not Installed"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2402
msgid "Installed But Not Activated"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2410
msgid "Required Update not Available"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2413
msgid "Requires Update"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2416
msgid "Update recommended"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2425
#, php-format
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2471
#, php-format
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2475
#, php-format
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2479
#, php-format
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2483
#, php-format
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2565
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2573
msgid "Installed version:"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2581
msgid "Minimum required version:"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2593
msgid "Available version:"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2616
msgid "No plugins to install, update or activate."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2630
msgid "Plugin"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2631
msgid "Source"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2632
msgid "Type"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2636
msgid "Version"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2637
msgid "Status"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2686
#, php-format
msgid "Install %2$s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2691
#, php-format
msgid "Update %2$s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2697
#, php-format
msgid "Activate %2$s"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2767
msgid "Upgrade message from the plugin author:"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2800
msgid "Install"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2806
msgid "Update"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2809
msgid "Activate"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2840
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2842
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2883
msgid "No plugins are available to be installed at this time."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2885
msgid "No plugins are available to be updated at this time."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:2991
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3017
msgid "No plugins are available to be activated at this time."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3241
msgid "Plugin activation failed."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3581
#, php-format
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3584
#, php-format
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3586
#, php-format
msgid "The installation of %1$s failed."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3590
msgid ""
"The installation and activation process is starting. This process may take a "
"while on some hosts, so please be patient."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3592
#, php-format
msgid "%1$s installed and activated successfully."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3593
msgid "All installations and activations have been completed."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3595
#, php-format
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3598
msgid ""
"The installation process is starting. This process may take a while on some "
"hosts, so please be patient."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3600
#, php-format
msgid "%1$s installed successfully."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3601
msgid "All installations have been completed."
msgstr ""

#: framework/includes/yolo-dash/tgmpa/class-tgm-plugin-activation.php:3603
#, php-format
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:13
msgid "Thank you for purchasing Motor Theme!"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:45
#: framework/includes/yolo-dash/yolo-setup-install.php:46
msgid "Motor"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:58
msgid "Yolo Dashboard"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:59
msgid "Dashboard"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:107
msgid "Yolo Theme"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:112
msgid "Welcome to Yolo Theme!"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:113
msgid "We've assembled some links to get you started"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:119
msgid "Get start"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:120
msgid "Customize Your Site"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:121
msgid "or"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:121
msgid "change your theme completely"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:128
#: framework/includes/yolo-dash/yolo-setup-install.php:141
msgid "Keep in Touch"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:130
msgid "View Footer Blocks"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:131
msgid "Read Documentation"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:132
msgid "Request Support"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:133
msgid "View Your Site"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:160
#: framework/includes/yolo-dash/yolo-setup-install.php:165
msgid "Purchase Code Verify"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:161
#: framework/includes/yolo-dash/yolo-setup-install.php:166
msgid "Install Plugin"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:162
#: framework/includes/yolo-dash/yolo-setup-install.php:167
msgid "Import Data"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:168
msgid "Document and Support"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:199
#, php-format
msgid ""
"Input the ThemeForest purchase code to be able to download, update and fully "
"access to %s"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:202
msgid "ThemeForest Purchase Code:"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:203
msgid "Enter Purchase Code *, eg. abcd-efgh-ikml-1234"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:205
msgid "How to get License key?"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:209
msgid "Validated"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:231
msgid "Install All Plugins"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:235
msgid "You installed Successfull!"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:259
#, php-format
msgid ""
"Please <a href = \"%s\">Install</a> and <a href = \"%s\">Active</a> Motor "
"Framework"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:295
msgid "Purchase code verification failed."
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:308
msgid "Purchase code is activated"
msgstr ""

#: framework/includes/yolo-dash/yolo-setup-install.php:316
msgid "Please enter purchase code."
msgstr ""

#: framework/vc_extension/update_params.php:21
msgid "CSS Animation"
msgstr ""

#: framework/vc_extension/update_params.php:25
msgid "Fade In"
msgstr ""

#: framework/vc_extension/update_params.php:26
msgid "Fade Top to Bottom"
msgstr ""

#: framework/vc_extension/update_params.php:27
msgid "Fade Bottom to Top"
msgstr ""

#: framework/vc_extension/update_params.php:28
msgid "Fade Left to Right"
msgstr ""

#: framework/vc_extension/update_params.php:29
msgid "Fade Right to Left"
msgstr ""

#: framework/vc_extension/update_params.php:30
msgid "Bounce In"
msgstr ""

#: framework/vc_extension/update_params.php:31
msgid "Bounce Top to Bottom"
msgstr ""

#: framework/vc_extension/update_params.php:32
msgid "Bounce Bottom to Top"
msgstr ""

#: framework/vc_extension/update_params.php:33
msgid "Bounce Left to Right"
msgstr ""

#: framework/vc_extension/update_params.php:34
msgid "Bounce Right to Left"
msgstr ""

#: framework/vc_extension/update_params.php:35
msgid "Zoom In"
msgstr ""

#: framework/vc_extension/update_params.php:36
msgid "Flip Vertical"
msgstr ""

#: framework/vc_extension/update_params.php:37
msgid "Flip Horizontal"
msgstr ""

#: framework/vc_extension/update_params.php:38
msgid "Bounce"
msgstr ""

#: framework/vc_extension/update_params.php:39
msgid "Flash"
msgstr ""

#: framework/vc_extension/update_params.php:40
msgid "Shake"
msgstr ""

#: framework/vc_extension/update_params.php:41
msgid "Pulse"
msgstr ""

#: framework/vc_extension/update_params.php:43
msgid "Rubber band"
msgstr ""

#: framework/vc_extension/update_params.php:44
msgid "Wobble"
msgstr ""

#: framework/vc_extension/update_params.php:45
msgid "Tada"
msgstr ""

#: framework/vc_extension/update_params.php:47
msgid ""
"Select type of animation if you want this element to be animated when it "
"enters into the browsers viewport. Note: Works only in modern browsers."
msgstr ""

#: framework/vc_extension/update_params.php:48
#: framework/vc_extension/update_params.php:83
#: framework/vc_extension/update_params.php:118
#: framework/vc_extension/update_params.php:131
#: framework/vc_extension/update_params.php:143
#: framework/vc_extension/update_params.php:155
#: framework/vc_extension/update_params.php:167
#: framework/vc_extension/update_params.php:183
#: framework/vc_extension/update_params.php:194
msgid "Yolo Options"
msgstr ""

#: framework/vc_extension/update_params.php:53
msgid "Animation Duration"
msgstr ""

#: framework/vc_extension/update_params.php:56
msgid ""
"Duration in seconds. You can use decimal points in the value. Use this field "
"to specify the amount of time the animation plays. <em>The default value "
"depends on the animation, leave blank to use the default.</em>"
msgstr ""

#: framework/vc_extension/update_params.php:88
msgid "Animation Delay"
msgstr ""

#: framework/vc_extension/update_params.php:91
msgid ""
"Delay in seconds. You can use decimal points in the value. Use this field to "
"delay the animation for a few seconds, this is helpful if you want to chain "
"different effects one after another above the fold."
msgstr ""

#: framework/vc_extension/update_params.php:135
msgid "Show background overlay"
msgstr ""

#: framework/vc_extension/update_params.php:137
msgid "Hide or Show overlay on background images."
msgstr ""

#: framework/vc_extension/update_params.php:139
msgid "Hide, please"
msgstr ""

#: framework/vc_extension/update_params.php:140
msgid "Show Overlay Color"
msgstr ""

#: framework/vc_extension/update_params.php:141
msgid "Show Overlay Image"
msgstr ""

#: framework/vc_extension/update_params.php:147
msgid "Image Overlay:"
msgstr ""

#: framework/vc_extension/update_params.php:150
msgid "Upload image overlay."
msgstr ""

#: framework/vc_extension/update_params.php:159
msgid "Overlay color"
msgstr ""

#: framework/vc_extension/update_params.php:161
msgid "Select color for background overlay."
msgstr ""

#: framework/vc_extension/update_params.php:172
msgid "Overlay opacity"
msgstr ""

#: framework/vc_extension/update_params.php:178
msgid "Select opacity for overlay."
msgstr ""

#: framework/vc_extension/update_params.php:187
msgid "Parallax Effect"
msgstr ""

#: framework/vc_extension/update_params.php:189
msgid "Disable or enable parallax effect for background"
msgstr ""

#: framework/vc_extension/update_params.php:191
msgid "Disable, please"
msgstr ""

#: framework/vc_extension/update_params.php:192
msgid "Enable Parallax"
msgstr ""

#: functions.php:51
msgid "Continue Shopping"
msgstr ""

#: sidebar.php:18
msgid "Blog Sidebar"
msgstr ""

#: templates/archive-heading.php:21
msgid "Nothing Found"
msgstr ""

#: templates/archive-heading.php:24 templates/breadcrumb.php:19
msgid "Blog"
msgstr ""

#: templates/archive-heading.php:31
msgid "Tags: "
msgstr ""

#: templates/archive-heading.php:33
#, php-format
msgid "Author: %s"
msgstr ""

#: templates/archive-heading.php:35
#, php-format
msgid "Daily Archives: %s"
msgstr ""

#: templates/archive-heading.php:37
#, php-format
msgid "Monthly Archives: %s"
msgstr ""

#: templates/archive-heading.php:37
msgctxt "monthly archives date format"
msgid "F Y"
msgstr ""

#: templates/archive-heading.php:39
#, php-format
msgid "Yearly Archives: %s"
msgstr ""

#: templates/archive-heading.php:39
msgctxt "yearly archives date format"
msgid "Y"
msgstr ""

#: templates/archive-heading.php:41
#, php-format
msgid "Search Results for: %s"
msgstr ""

#: templates/archive-heading.php:43
msgid "Asides"
msgstr ""

#: templates/archive-heading.php:45
msgid "Galleries"
msgstr ""

#: templates/archive-heading.php:49
msgid "Videos"
msgstr ""

#: templates/archive-heading.php:51
msgid "Quotes"
msgstr ""

#: templates/archive-heading.php:53
msgid "Links"
msgstr ""

#: templates/archive-heading.php:55
msgid "Statuses"
msgstr ""

#: templates/archive-heading.php:57
msgid "Audios"
msgstr ""

#: templates/archive-heading.php:59
msgid "Chats"
msgstr ""

#: templates/archive-heading.php:61
msgid "Archives"
msgstr ""

#: templates/archive/content-none.php:15
#, php-format
msgid ""
"Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: templates/archive/content-none.php:19
msgid ""
"Sorry, but nothing matched your search terms. Please try again with "
"different keywords."
msgstr ""

#: templates/archive/content-none.php:24
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#: templates/archive/content-search.php:33
msgid "View more"
msgstr ""

#: templates/archive/content.php:58
msgid "Read more"
msgstr ""

#: templates/archive/post-meta.php:30 templates/single-blog/post-meta.php:28
msgid "<i class=\"fa fa-comments-o p-color\"></i> 0 Comment"
msgstr ""

#: templates/archive/post-meta.php:30 templates/single-blog/post-meta.php:28
msgid "<i class=\"fa fa-comments-o p-color\"></i> 1 Comment"
msgstr ""

#: templates/archive/post-meta.php:30 templates/single-blog/post-meta.php:28
msgid "<i class=\"fa fa-comments-o p-color\"></i> % Comments"
msgstr ""

#: templates/comments.php:22
msgid "No Comments"
msgstr ""

#: templates/comments.php:22
msgid "One Comment"
msgstr ""

#: templates/comments.php:22
#, php-format
msgid "There are % comments"
msgstr ""

#: templates/header/search-box.php:29 templates/header/search-popup.php:23
#: templates/header/search-with-category.php:20
msgid "Enter keyword to search"
msgstr ""

#: templates/header/search-box.php:31 woocommerce/archive-product.php:150
msgid "Search"
msgstr ""

#: templates/header/search-popup.php:36
msgid "Search Products..."
msgstr ""

#: templates/maintenance.php:96
msgid "Days"
msgstr ""

#: templates/maintenance.php:97
msgid "Hours"
msgstr ""

#: templates/maintenance.php:98
msgid "Minutes"
msgstr ""

#: templates/maintenance.php:99
msgid "Seconds"
msgstr ""

#: templates/popup-window.php:30
msgid "Don't show this popup again"
msgstr ""

#: templates/single-blog/post-nav.php:28
msgctxt "Previous post link"
msgid ""
"<div class=\"post-navigation-left\"><i class=\"post-navigation-icon fa fa-"
"angle-double-left\"></i> <div class=\"post-navigation-label\">Prev</div></"
"div> <div class=\"post-navigation-content\"> <div class=\"post-navigation-"
"title\">%title </div> </div> "
msgstr ""

#: templates/single-blog/post-nav.php:29
msgctxt "Next post link"
msgid ""
"<div class=\"post-navigation-content\"> <div class=\"post-navigation-title\">"
"%title</div></div> <div class=\"post-navigation-right\"><i class=\"post-"
"navigation-icon fa fa-angle-double-right\"></i> <div class=\"post-navigation-"
"label\">Next</div></div>"
msgstr ""

#: templates/single-blog/post-tags.php:13
msgid "Tags"
msgstr ""

#: templates/social-share.php:24
msgid "Share:"
msgstr ""

#: templates/switch-selector.php:20
msgid "Purchase Now"
msgstr ""

#: templates/switch-selector.php:48
msgid "Boxed Background"
msgstr ""

#: templates/switch-selector.php:78
msgid "Reset"
msgstr ""

#: templates/switch-selector.php:85
msgid "Demos"
msgstr ""

#: woocommerce/archive-product.php:92
msgid "All products loaded"
msgstr ""

#: woocommerce/archive-product.php:142
msgid "Filter"
msgstr ""

#: woocommerce/archive-product.php:176
msgid "Search Products"
msgstr ""

#: woocommerce/cart/cart-shipping.php:51
#, php-format
msgid "Estimate for %s."
msgstr ""

#: woocommerce/cart/cart-shipping.php:52
msgid "Change address"
msgstr ""

#: woocommerce/cart/cart-shipping.php:54
msgid "This is only an estimate. Prices will be updated during checkout."
msgstr ""

#: woocommerce/cart/cart-shipping.php:61
msgid "Enter your address to view shipping options."
msgstr ""

#: woocommerce/cart/cart-shipping.php:63
msgid ""
"There are no shipping methods available. Please ensure that your address has "
"been entered correctly, or contact us if you need any help."
msgstr ""

#: woocommerce/cart/cart-shipping.php:66
#, php-format
msgid "No shipping options were found for %s."
msgstr ""

#: woocommerce/cart/cart-shipping.php:67
msgid "Enter a different address"
msgstr ""

#: woocommerce/cart/cross-sells.php:55
msgid "You may be interested in&hellip;"
msgstr ""

#: woocommerce/cart/mini-cart.php:101
msgid "An empty cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:102
msgid "You have no item in your shopping cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:110
msgid "Total"
msgstr ""

#: woocommerce/cart/mini-cart.php:116
msgid "View Cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:119
msgid "Checkout"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:24
msgid "Calculate Shipping"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:30
msgid "Select a country&hellip;"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:47
#: woocommerce/cart/shipping-calculator.php:53
#: woocommerce/cart/shipping-calculator.php:67
msgid "State / county"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:54
msgid "Select a state&hellip;"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:76
msgid "City"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:84
msgid "Postcode / Zip"
msgstr ""

#: woocommerce/cart/shipping-calculator.php:89
msgid "Update Totals"
msgstr ""

#: woocommerce/checkout/form-coupon.php:18
msgid "Have a coupon?"
msgstr ""

#: woocommerce/checkout/form-coupon.php:18
msgid "Click here to enter your code"
msgstr ""

#: woocommerce/checkout/form-coupon.php:29
msgid "Coupon code"
msgstr ""

#: woocommerce/checkout/form-coupon.php:33
msgid "Apply Coupon"
msgstr ""

#: woocommerce/checkout/form-login.php:18
msgid "Returning customer?"
msgstr ""

#: woocommerce/checkout/form-login.php:19
msgid "Click here to login"
msgstr ""

#: woocommerce/checkout/form-login.php:28
msgid ""
"If you have shopped with us before, please enter your details in the boxes "
"below. If you are a new customer please proceed to the Billing &amp; "
"Shipping section."
msgstr ""

#: woocommerce/checkout/form-shipping.php:26
msgid "Shipping Address"
msgstr ""

#: woocommerce/checkout/form-shipping.php:31
msgid "Ship to a different address?"
msgstr ""

#: woocommerce/checkout/form-shipping.php:57
msgid "Additional Information"
msgstr ""

#: woocommerce/checkout/title.php:12
msgid "Returning customers?"
msgstr ""

#: woocommerce/content-product_reset.php:19
#, php-format
msgid "Filters active %s(%s)%s"
msgstr ""

#: woocommerce/content-product_reset.php:28
#, php-format
msgid "Search results for %s&ldquo;%s&rdquo;%s"
msgstr ""

#: woocommerce/content-product_reset.php:36
#, php-format
msgid "Products tagged: %s&ldquo;%s&rdquo;%s"
msgstr ""

#: woocommerce/global/quantity-input.php:39
msgctxt "Product quantity input tooltip"
msgid "Qty"
msgstr ""

#: woocommerce/loop/add-to-cart.php:31 woocommerce/loop/sale-flash.php:33
#: woocommerce/single-product/sale-flash.php:32
msgid "Sold"
msgstr ""

#: woocommerce/loop/no-products-found.php:12
msgid "No products found matching your search!"
msgstr ""

#: woocommerce/loop/pagination.php:54
msgid "Load more"
msgstr ""

#: woocommerce/loop/quick-view.php:16
msgid "Quick view"
msgstr ""

#: woocommerce/loop/sale-flash.php:21
#: woocommerce/single-product/sale-flash.php:20
msgid "Sale!"
msgstr ""

#: woocommerce/myaccount/form-login.php:34
msgid "Sign In"
msgstr ""

#: woocommerce/myaccount/form-login.php:35
msgid "Sign in with your created account"
msgstr ""

#: woocommerce/myaccount/form-login.php:42
msgid "Username or email address"
msgstr ""

#: woocommerce/myaccount/form-login.php:54
msgid "Login"
msgstr ""

#: woocommerce/myaccount/form-login.php:56
msgid "Remember me"
msgstr ""

#: woocommerce/myaccount/form-login.php:60
msgid "Forgot your password?"
msgstr ""

#: woocommerce/myaccount/form-login.php:73
msgid "Create an new account"
msgstr ""

#: woocommerce/myaccount/form-login.php:74
msgid "Create an account to track your orders, create a wishlist and more"
msgstr ""

#: woocommerce/myaccount/form-login.php:107
msgid "Register"
msgstr ""

#: woocommerce/product-searchform.php:25
msgid "Search products&hellip;"
msgstr ""

#: woocommerce/product-searchform.php:26
msgctxt "submit button"
msgid "Search"
msgstr ""

#: woocommerce/quick-view/rating.php:27
#, php-format
msgid "%s customer review"
msgid_plural "%s customer reviews"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/quick-view/rating.php:31
#: woocommerce/single-product/rating.php:29
#, php-format
msgid "out of %s5%s"
msgstr ""

#: woocommerce/quick-view/rating.php:32
#: woocommerce/single-product/rating.php:30
#, php-format
msgid "based on %s customer rating"
msgid_plural "based on %s customer ratings"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/add-to-cart/variable.php:35
msgid "This product is currently out of stock and unavailable."
msgstr ""

#: woocommerce/single-product/add-to-cart/variable.php:90
#: woocommerce/single-product/add-to-cart/variable.php:99
msgid "Clear"
msgstr ""

#: woocommerce/single-product/add-to-cart/variation.php:20
msgid ""
"Sorry, this product is unavailable. Please choose a different combination."
msgstr ""

#: woocommerce/single-product/meta.php:20
msgid "In stock"
msgstr ""

#: woocommerce/single-product/meta.php:32
msgid "SKU:"
msgstr ""

#: woocommerce/single-product/meta.php:32
msgid "N/A"
msgstr ""

#: woocommerce/single-product/meta.php:36
msgid "Availability:"
msgstr ""

#: woocommerce/single-product/meta.php:39
msgid "Shipping Weight:"
msgstr ""

#: woocommerce/single-product/meta.php:42
msgid "<label>Category:</label>"
msgid_plural "<label>Categories:</label>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:44
msgid "<label>Tag:</label>"
msgid_plural "<label>Tag:</label>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/product-image.php:164
#: woocommerce/single-product/product-image.php:321
msgid "Awaiting product image"
msgstr ""

#: woocommerce/single-product/rating.php:33
#, php-format
msgid "(%s review)"
msgid_plural "%s reviews"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/related.php:46
msgid "Related Products"
msgstr ""

#: woocommerce/single-product/review.php:49
msgid "Your comment is awaiting approval"
msgstr ""

#: woocommerce/single-product/review.php:58
msgid "verified owner"
msgstr ""

#: woocommerce/single-product/up-sells.php:54
msgid "You may also like&hellip;"
msgstr ""

#. Theme Name of the plugin/theme
msgid "Yolo Motor"
msgstr ""

#. Theme URI of the plugin/theme
msgid "http://yolotheme.com/"
msgstr ""

#. Description of the plugin/theme
msgid ""
"Motor Vehikal is Multi-Purpose Woocomerce WordPress theme with power full "
"customize theme options and page options. Base on Redux Framework."
msgstr ""

#. Author of the plugin/theme
msgid "YoloTheme"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://facebook.com/yolotheme/"
msgstr ""
