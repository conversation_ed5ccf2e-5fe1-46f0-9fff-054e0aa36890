<?php
/**
 *  
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2015, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
*/

global $yolo_motor_options;

$prefix = 'yolo_';

$header_class = array('yolo-main-header', 'header-1', 'header-desktop-wrapper');
$header_nav_wrapper = array('yolo-header-nav-wrapper');
$header_sticky_effect = '';
// @TODO: need process page_enable_header_customize from header.php
$header_layout_float = get_post_meta(get_the_ID(), $prefix . 'header_layout_float',true);
if ((($header_layout_float === '') || ($header_layout_float == '-1') || is_search() || $header_layout_float === false) && !is_404()) {
	$header_layout_float = $yolo_motor_options['header_1_layout_float'];
}

if ( 1 == $header_layout_float ) {
	$header_class[] = 'header-float';
}

$header_sticky = $yolo_motor_options['header_sticky'];

if ( 1 == $header_sticky ) {
	wp_enqueue_script('sticky-header');
	$header_nav_wrapper[] = 'header-sticky';
    $header_nav_wrapper[] = 'animate';
    $header_sticky_effect = isset($yolo_motor_options['header_sticky_effect']) ? $yolo_motor_options['header_sticky_effect'] : 'slideDown,slideUp';
    $header_sticky_scheme = isset($yolo_motor_options['header_sticky_scheme']) ? $yolo_motor_options['header_sticky_scheme'] : 'inherit';
    $header_nav_wrapper[] = 'sticky-scheme-' . $header_sticky_scheme;
}

$page_menu 				= get_post_meta(get_the_ID(), $prefix . 'page_menu',true);
$header_nav_layout 		= isset($yolo_motor_options['header_1_nav_layout']) ? $yolo_motor_options['header_1_nav_layout'] : '';

if ( 'nav-fullwith' == $header_nav_layout ) {
	$header_nav_wrapper[] = $header_nav_layout;
}
?>
<style>
.md {
	display: flex;
	line-height: 1.2;
	flex-direction: column;
	align-items: first baseline;
	float: left;
	line-height: 1.2;
	height: 60px;
	justify-content: center;
	padding: 0 30px;
	margin: auto !important;
}
.md * {
	white-space: nowrap;
	font-family: "AkzidenzGroteskPro", sans-serif;
	font-size: 12px;
	font-weight: 700;
	text-transform: uppercase;
    display: block;
    line-height: 1.2;
}
</style>
<?php 
$user = wp_get_current_user();
$main_user_id = get_main_b2b_admin_id($user->ID);
$company_code = get_user_meta($main_user_id, '_companycode', true);
if($company_code == "3090"){
    $customer_service = '<span>Customer Service:</span><a href="tel:+18005411418" class="orange">****** 541 1418</a>';
}else{
    $customer_service = '<span>Customer Service:</span><a href="+31455678877" class="orange">+31 45 567 8877</a>';
}
?>

<header id="yolo-header" data-code="<?php echo $country_code; ?>" class="<?php echo join(' ', $header_class) ?>">
	<div class="yolo-header-nav-above text-left">
		<div class="container <?php echo $header_nav_layout; ?>">
			<div class="fl">
				<?php yolo_get_template('header/header-logo' ); ?>
			</div>
			<div class="md">
                <?php if (is_user_logged_in()) { ?>
                <?php echo $customer_service; ?>
                <?php } ?>
			</div>
			<div class="fr">
				<?php yolo_get_template('header/header-customize-right' ); ?>
			</div>
		</div>
	</div>
	<!-- <div class="<?php // echo join(' ', $header_nav_wrapper) ?>" data-effect ="<?php // echo esc_attr($header_sticky_effect);?>">
		<div class="container">
			<div class="yolo-header-wrapper">
				<div class="header-center">
					<?php // if (has_nav_menu('primary')) : ?>
						<div id="primary-menu" class="menu-wrapper">
							<?php
							// wp_nav_menu( array(
							// 	'theme_location'  => 'primary',
							// 	'menu'            => $page_menu,
							// 	'container'       => '',
							// 	'menu_class'      => 'yolo-main-menu nav-collapse navbar-nav',
							// 	'menu_id'         => 'main-menu',
							// 	'fallback_cb'     => 'please_set_menu',
							// 	'walker'          => new Yolo_MegaMenu_Walker(),
							// ) );
							?>
						</div>
					<?php // endif; ?>
					<?php // yolo_get_template('header/header-customize-nav' ); ?>
				</div>
			</div>
		</div>
	</div> -->
</header>

<div id="new-list-popup" style="display:none;">
    <div id="new-list-form">
        <span class="close_list_popup" onclick="jQuery(this).parent().parent().fadeOut();">×</span>
        <h2><?php _e('Create New List', 'woocommerce'); ?></h2>
        <input type="text" id="list_name" class="d-block w100 mt-3" name="list_name" placeholder="Name" required style="background: #fff;">
        <button type="button" id="new-list-submit" class="btn orange-bg white px-3 mt-3">Create</button>
    </div>
</div>
<script>
    jQuery(document).ready(function($) {
        // Handle selection change in dropdown
        $(document).on('click', '.create_new', function() {
            $('#new-list-popup').fadeIn();
        });

        // Handle the form submission for creating a new list
        $('#new-list-submit').on('click', function(e) {
            // e.preventDefault();
            var listName = $('#list_name').val();
            $('#new-list-submit').addClass('btn--loading');
            // Send AJAX request to create a new list
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'create_new_list',
                    list_name: listName,
                    security: '<?php echo wp_create_nonce('create_new_list_nonce'); ?>'
                },
                success: function(response) {
                    console.log(response);                    
                    if (response.success) {
                        // Reload the page to update the dropdown options
                        app_msg("List created");
                        if(window.location.pathname == '/quick-order/'){
                            // window.location.reload();
                            $('.shopping-lists-dropdown').prepend('<span class="shopping-list-item add-to-list-button" data-list_id="'+response.data.key+'" onclick="add_products_to_list_button(jQuery(this))">'+listName+'</span>');
                        }else{
                            location.reload();
                        }
                    } else {
                        app_msg('Error: ' + response.data);
                    }
                    $('#new-list-popup').fadeOut();
                    setTimeout(function() {
                        $('#new-list-submit').removeClass('btn--loading');
                    }, 800);
                    $('.shopping-lists-dropdown').toggleClass('show');
                },
                error: function(xhr, status, error) {
                    console.log('Error:', error);
                    $('#new-list-popup').fadeOut();
                    setTimeout(function() {
                        $('#new-list-submit').removeClass('btn--loading');
                    }, 800);
                    $('.shopping-lists-dropdown').toggleClass('show');
                }
            });
        });
    });
</script>
